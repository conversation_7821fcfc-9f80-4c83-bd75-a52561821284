/* YOLO训练平台样式文件 */

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding: 15px 2%;
    max-width: 100%;
    overflow-x: hidden;
}

/* 四栏布局：列宽 30% 30% 20% 20%，列间距 2%，左右内边距 2%（总间距 10%） */
.main-row {
    display: grid;
    grid-template-columns: 27% 27% 18% 18%;
    column-gap: 2%;
    height: calc(100vh - 30px);
    min-height: 600px;
    width: 100%;
    max-width: 100vw;
    overflow-x: auto;
}

.column {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;
}

/* 取消旧的 flex 固定宽度与右侧 margin，改由 Grid 控制 */
.column:nth-child(1),
.column:nth-child(2),
.column:nth-child(3),
.column:nth-child(4) {
    min-width: 0;
}

/* 列头部样式 */
.column-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    z-index: 10;
    flex-shrink: 0;
}

.column-header h5 {
    margin: 0 0 10px 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.column-header .bi {
    font-size: 1.1em;
}

/* 列内容区域 */
.column-content {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    box-sizing: border-box;
    position: relative;
}

/* 类别管理区域优化 */
#classContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-content: flex-start;
}

/* 紧凑型类别卡片设计 */
.class-card {
    flex: 0 0 calc(50% - 4px);
    min-width: 120px;
    max-width: 200px;
    margin: 0;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    background: #fff;
}

.class-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    transform: translateY(-1px);
}

.class-card.selected {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 0 0 2px rgba(40,167,69,0.2);
}

.class-card .card-body {
    padding: 8px 10px;
}

.class-card .card-title {
    font-size: 0.9em;
    font-weight: 600;
    margin: 0 0 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 顶部右上角两个角标并排放置 */
.class-card { position: relative; }
.class-card .badge { box-shadow: 0 1px 3px rgba(0,0,0,0.15); }
.class-card .badge-right-group {
    position: absolute;
    top: -6px;
    right: -6px;
    display: flex;
    gap: 6px;
    z-index: 20;
}

/* 隐藏原有的统计标签 */
.sample-stats {
    display: none;
}

.stat-badge {
    display: none;
}

.stat-positive {
    display: none;
}

.stat-negative {
    display: none;
}

/* 删除按钮优化 */
.class-card .btn-outline-danger {
    position: absolute;
    top: 4px;
    right: 4px;
    padding: 2px 4px;
    font-size: 0.7em;
    border: none;
    background: rgba(220,53,69,0.1);
    color: #dc3545;
    border-radius: 3px;
}

.class-card .btn-outline-danger:hover {
    background: #dc3545;
    color: white;
}

/* 右键上下文菜单 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 120px;
    padding: 4px 0;
    display: none;
}

.context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 0.9em;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-menu-item:hover {
    background: #f8f9fa;
}

.context-menu-item.danger:hover {
    background: #f8d7da;
    color: #721c24;
}

/* 拖拽排序样式 */
.class-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.class-card.drag-over {
    border-color: #ffc107;
    background: #fff3cd;
}

/* 样本标注区域优化 */
.annotation-container {
    position: relative;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    min-height: 400px;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.annotation-container.drag-over {
    border-color: #007bff;
    background: #e7f3ff;
}

.canvas-container {
    position: relative;
    max-width: 100%;
    max-height: calc(100% - 40px);
    width: 100%;
    height: 100%;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Canvas自适应高度 */
#annotationCanvas {
    max-width: 100%;
    max-height: 100%;
    height: auto !important;
    border-radius: 4px;
    object-fit: contain;
}

/* 拖拽提示优化 */
#dropHint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    color: #6c757d;
    z-index: 1;
}

#dropHint .bi {
    opacity: 0.5;
}

/* 按钮组优化 - 解决重叠问题 */
.column-header .d-flex {
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
    position: relative;
    z-index: 20;
}

.column-header .btn-sm {
    padding: 6px 12px;
    font-size: 0.85em;
    white-space: nowrap;
    position: relative;
    z-index: 21;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    transition: all 0.2s ease;
}

.column-header .btn-sm:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-1px);
}

.column-header .btn-primary {
    background: rgba(0,123,255,0.8);
    border-color: rgba(0,123,255,0.6);
}

.column-header .btn-primary:hover {
    background: rgba(0,123,255,1);
    border-color: rgba(0,123,255,0.8);
}

/* 输入框优化 */
.column-header .form-control {
    background: rgba(255,255,255,0.9);
    border: 1px solid rgba(255,255,255,0.3);
    color: #333;
    position: relative;
    z-index: 21;
}

.column-header .form-control:focus {
    background: white;
    border-color: rgba(0,123,255,0.5);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.column-header .form-select {
    background: rgba(255,255,255,0.9);
    border: 1px solid rgba(255,255,255,0.3);
    color: #333;
    position: relative;
    z-index: 21;
}

.column-header .form-select:focus {
    background: white;
    border-color: rgba(0,123,255,0.5);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 模型训练区域优化 */
.log-area {
    background: #1e1e1e;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 0.8em;
    padding: 10px;
    border-radius: 4px;
    height: 100%;
    min-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    box-sizing: border-box;
}

/* 强制日志容器内文字为白色，包括嵌套元素 */
.log-area, .log-area * { color: #fff !important; }

/* 确保日志区域内文本均为白色，包括占位的 muted 文本 */
.log-area, .log-area * {
    color: #ffffff !important;
}

/* 识别结果区域优化 */
.result-area {
    height: 100%;
    min-height: 200px;
    overflow-y: auto;
    box-sizing: border-box;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.result-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 6px;
}

/* 表单控件优化 */
.form-control-sm {
    font-size: 0.85em;
    padding: 4px 8px;
}

.form-range {
    margin: 8px 0;
}

/* 模态框优化 */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-row {
        grid-template-columns: 1fr;
        row-gap: 10px;
        height: auto;
    }
    .column {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .main-row {
        gap: 10px;
    }
    
    .column-header {
        padding: 10px;
    }
    
    .column-content {
        padding: 10px;
    }
    
    .class-card {
        flex: 0 0 calc(50% - 4px);
        min-width: 100px;
    }
    
    .column-header .d-flex {
        flex-direction: column;
        gap: 6px;
    }
    
    .column-header .btn-sm {
        font-size: 0.75em;
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .class-card {
        flex: 0 0 100%;
    }
    
    .main-row {
        gap: 8px;
    }
}

/* 滚动条美化 */
.column-content::-webkit-scrollbar,
.log-area::-webkit-scrollbar {
    width: 6px;
}

.column-content::-webkit-scrollbar-track,
.log-area::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.column-content::-webkit-scrollbar-thumb,
.log-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.column-content::-webkit-scrollbar-thumb:hover,
.log-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.class-card {
    animation: fadeIn 0.3s ease;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip-custom:hover::after {
    opacity: 1;
}

/* 成功/错误状态 */
.success-state {
    border-color: #28a745 !important;
    background: #f8fff9 !important;
}

.error-state {
    border-color: #dc3545 !important;
    background: #fff8f8 !important;
}

/* 隐藏文件输入 */
#fileInput {
    display: none;
}