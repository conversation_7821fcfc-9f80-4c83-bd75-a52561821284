// 全局变量
let fabricCanvas;
let currentImage = null;
let currentImageFile = null; // 保存原始图片文件
let selectedClass = null; // 当前选中的类别（单选模式）
let currentFileName = '';
let wsConnection = null;
let annotations = [];

// 初始化
$(document).ready(function() {
    initFabricCanvas();
    loadClasses();
    loadModels();
    setupEventListeners();
    setupWebSocket();
    initContextMenu();
    initAnnotationContextMenu(); // 初始化标注右键菜单
});

// 右键菜单相关
let contextMenu = null;
let annotationContextMenu = null;
let currentAnnotationTarget = null;
let draggedElement = null;

function initContextMenu() {
    // 创建类别右键菜单
    contextMenu = document.createElement('div');
    contextMenu.className = 'context-menu';
    contextMenu.innerHTML = `
        <div class="context-menu-item" onclick="openRenameClassModal()">重命名</div>
        <div class="context-menu-item" onclick="deleteClass()">删除类别</div>
    `;
    document.body.appendChild(contextMenu);
    
    // 点击其他地方隐藏类别菜单
    document.addEventListener('click', function(e) {
        // 检查是否点击的是类别菜单
        if (!contextMenu.contains(e.target)) {
            hideContextMenu();
        }
    });
}

function showContextMenu(event, className) {
    event.preventDefault();
    event.stopPropagation();
    
    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';
    contextMenu.dataset.className = className;
}

function hideContextMenu() {
    if (contextMenu) {
        contextMenu.style.display = 'none';
    }
}

// 通用 Modal 工具
function showInfoModal(title, message) {
    let modalEl = document.getElementById('infoGenericModal');
    if (!modalEl) {
        modalEl = document.createElement('div');
        modalEl.id = 'infoGenericModal';
        modalEl.className = 'modal fade';
        modalEl.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body"></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                    </div>
                </div>
            </div>`;
        document.body.appendChild(modalEl);
    }
    modalEl.querySelector('.modal-title').textContent = title || '提示';
    modalEl.querySelector('.modal-body').textContent = message || '';
    new bootstrap.Modal(modalEl).show();
}

function showConfirmModal(title, message, onConfirm) {
    let modalEl = document.getElementById('confirmGenericModal');
    if (!modalEl) {
        modalEl = document.createElement('div');
        modalEl.id = 'confirmGenericModal';
        modalEl.className = 'modal fade';
        modalEl.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body"></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-danger" id="confirmOkBtn">确定</button>
                    </div>
                </div>
            </div>`;
        document.body.appendChild(modalEl);
    }
    modalEl.querySelector('.modal-title').textContent = title || '确认';
    modalEl.querySelector('.modal-body').textContent = message || '';
    const btn = modalEl.querySelector('#confirmOkBtn');
    btn.onclick = function() {
        if (typeof onConfirm === 'function') onConfirm();
        bootstrap.Modal.getInstance(modalEl).hide();
    };
    new bootstrap.Modal(modalEl).show();
}

// 重命名类别（使用 modal）
function openRenameClassModal() {
    const oldName = contextMenu.dataset.className;
    if (!oldName) return;
    let modalEl = document.getElementById('renameClassModal');
    if (!modalEl) {
        modalEl = document.createElement('div');
        modalEl.id = 'renameClassModal';
        modalEl.className = 'modal fade';
        modalEl.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">重命名类别</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="text" class="form-control" id="renameClassInput" placeholder="新名称">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="renameClassConfirm">确认</button>
                    </div>
                </div>
            </div>`;
        document.body.appendChild(modalEl);
    }
    modalEl.querySelector('#renameClassInput').value = oldName;
    modalEl.querySelector('#renameClassConfirm').onclick = function() {
        const newName = modalEl.querySelector('#renameClassInput').value.trim();
        if (!newName || newName === oldName) {
            bootstrap.Modal.getInstance(modalEl).hide();
            return;
        }
        fetch('/api/class/rename', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ old_name: oldName, new_name: newName })
        })
        .then(r => r.json())
        .then(data => {
            if (data.success !== false) {
                loadClasses();
            } else {
                showInfoModal('错误', '重命名失败: ' + (data.message || ''));
            }
        })
        .catch(err => {
            console.error('重命名错误:', err);
            showInfoModal('错误', '重命名失败');
        })
        .finally(() => {
            bootstrap.Modal.getInstance(modalEl).hide();
        });
    };
    new bootstrap.Modal(modalEl).show();
    hideContextMenu();
}

function deleteClass() {
    const className = contextMenu.dataset.className;
    if (!className) return;
    showConfirmModal('删除类别', `确定要删除类别 "${className}" 吗？`, function() {
        fetch('/api/class/delete', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ class_name: className })
        })
        .then(r => {
            if (r.status === 405) {
                return fetch('/api/class/delete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ class_name: className })
                });
            }
            return r;
        })
        .then(resp => resp.json())
        .then(data => {
            if (data.success !== false) {
                loadClasses();
                if (selectedClass === className) selectedClass = null;
            } else {
                showInfoModal('错误', '删除失败: ' + (data.message || ''));
            }
        })
        .catch(err => {
            console.error('删除错误:', err);
            showInfoModal('错误', '删除失败');
        });
    });
    hideContextMenu();
}

// 拖拽排序功能
function handleDragStart(event) {
    draggedElement = event.currentTarget;
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/html', event.target.outerHTML);
}

function handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
}

function handleDrop(event) {
    event.preventDefault();
    const container = document.getElementById('classContainer');
    const targetCard = event.target.closest('.class-card');
    if (!draggedElement || !targetCard || draggedElement === targetCard) {
        draggedElement = null;
        return;
    }
    const children = Array.from(container.children);
    const draggedIndex = children.indexOf(draggedElement);
    const targetIndex = children.indexOf(targetCard);
    if (draggedIndex < targetIndex) {
        container.insertBefore(draggedElement, targetCard.nextSibling);
    } else {
        container.insertBefore(draggedElement, targetCard);
    }
    draggedElement = null;

    // 持久化顺序
    const order = Array.from(container.querySelectorAll('.class-card .card-title')).map(e => e.textContent.trim());
    fetch('/api/class/reorder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ order })
    }).catch(() => {});
}

// 初始化Fabric.js画布
function initFabricCanvas() {
    const container = document.getElementById('annotationContainer');
    const containerWidth = container.clientWidth - 30;
    const containerHeight = 400;
    
    fabricCanvas = new fabric.Canvas('annotationCanvas', {
        width: containerWidth,
        height: containerHeight,
        backgroundColor: '#ffffff'
    });
    
    // 窗口大小变化时重新调整canvas
    window.addEventListener('resize', function() {
        if (currentImage) {
            centerImageInCanvas();
            // 延迟更新标注框位置，确保图片已重新定位
            setTimeout(updateAnnotationPositions, 100);
        }
    });
    
    // 监听对象选中事件
    fabricCanvas.on('selection:created', function(e) {
        updateSelectionControls(e.selected[0]);
    });
    
    fabricCanvas.on('selection:updated', function(e) {
        updateSelectionControls(e.selected[0]);
    });
    
    fabricCanvas.on('selection:cleared', function() {
        console.log('选择被清除');
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 拖拽上传
    const container = document.getElementById('annotationContainer');
    
    container.addEventListener('dragover', function(e) {
        e.preventDefault();
        container.classList.add('drag-over');
    });
    
    container.addEventListener('dragleave', function(e) {
        e.preventDefault();
        container.classList.remove('drag-over');
    });
    
    container.addEventListener('drop', function(e) {
        e.preventDefault();
        container.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith('image/')) {
            loadImageToCanvas(files[0]);
        }
    });
    
    // 置信度阈值滑块
    document.getElementById('confThreshold').addEventListener('input', function(e) {
        document.getElementById('confValue').textContent = e.target.value;
    });
    
    // 键盘快捷键
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Delete' && fabricCanvas.getActiveObject()) {
            fabricCanvas.remove(fabricCanvas.getActiveObject());
            fabricCanvas.renderAll();
        }
    });

    // 禁用画布默认右键菜单
    if (fabricCanvas && fabricCanvas.upperCanvasEl) {
        fabricCanvas.upperCanvasEl.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 检查是否点击在标注框上
            const target = fabricCanvas.findTarget(e, true);
            if (target && target.isAnnotation) {
                console.log('画布右键检测到标注框:', target);
                showAnnotationContextMenu(e, target);
            }
        });
    }
}

// 设置WebSocket连接
function setupWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/train/log`;
    
    wsConnection = new WebSocket(wsUrl);
    
    wsConnection.onmessage = function(event) {
        const logArea = document.getElementById('trainingLog');
        logArea.innerHTML += event.data + '\n';
        logArea.scrollTop = logArea.scrollHeight;
    };
    
    wsConnection.onerror = function(error) {
        console.log('WebSocket错误:', error);
    };
}

// 类别管理功能
function createClass() {
    // 显示创建类别模态框
    const modal = new bootstrap.Modal(document.getElementById('createClassModal'));
    modal.show();
}

function confirmCreateClass() {
    const className = document.getElementById('classNameInput').value.trim();
    
    if (!className) {
        showInfoModal('提示', '请输入类别名称');
        return;
    }
    
    fetch('/api/class/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
            class_name: className
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('createClassModal'));
            modal.hide();
            // 清空输入框
            document.getElementById('classNameInput').value = '';
            // 重新加载类别
            loadClasses();
        } else {
            showInfoModal('错误', '创建失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('创建类别错误:', error);
        showInfoModal('错误', '创建类别失败');
    });
}

function loadClasses() {
    fetch('/api/class/get-all')
    .then(response => response.json())
    .then(data => {
        const container = document.getElementById('classContainer');
        container.innerHTML = '';
        const list = Array.isArray(data) ? data : (data.categories || data.data || []);
        if (list) {
            list.forEach(cls => {
                const card = createClassCard(cls);
                container.appendChild(card);
            });
        }
    })
    .catch(error => {
        console.error('加载类别错误:', error);
    });
}

function createClassCard(classData) {
    const card = document.createElement('div');
    card.className = 'card class-card';
    card.style.position = 'relative';
    card.onclick = () => toggleClassSelection(classData.name, card);
    card.oncontextmenu = (event) => showContextMenu(event, classData.name);
    card.draggable = true;
    card.ondragstart = handleDragStart;
    card.ondragover = handleDragOver;
    card.ondrop = handleDrop;
    
    const positiveCount = Number(classData.positive_samples || 0);
    const negativeCount = Number(classData.negative_samples || 0);
    
    // 使用Bootstrap角标样式（始终显示正负样本数）
    let badgesHtml = `
        <div class=\"badge-right-group\">
            <span class=\"badge rounded-pill bg-success\">${positiveCount}</span>
            <span class=\"badge rounded-pill bg-danger\">${negativeCount}</span>
        </div>`;
    
    card.innerHTML = `
        <div class="card-body p-2">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-1">${classData.name}</h6>
            </div>
        </div>
        ${badgesHtml}
    `;
    
    return card;
}

function toggleClassSelection(className, cardElement) {
    // 清除之前的选择
    if (selectedClass) {
        const prevCard = document.querySelector('.class-card.selected');
        if (prevCard) prevCard.classList.remove('selected');
    }
    
    // 设置新的选择
    if (selectedClass === className) {
        selectedClass = null; // 取消选择
    } else {
        selectedClass = className;
        cardElement.classList.add('selected');
    }
}

// 注意：删除类别功能已绑定在右键菜单的 deleteClass() 中，此处不再提供卡片按钮删除

// 图片上传和标注功能
function uploadImage() {
    document.getElementById('fileInput').click();
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        loadImageToCanvas(file);
    }
}

function loadImageToCanvas(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        fabric.Image.fromURL(e.target.result, function(img) {
            // 清除现有内容
            fabricCanvas.clear();
            
            // 存储原始图片引用
            currentImage = img;
            currentImageFile = file; // 设置原始文件
            currentFileName = file.name;
            
            centerImageInCanvas();
            
            // 隐藏提示文字
            document.getElementById('dropHint').style.display = 'none';
            
            // 启用标注模式
            enableAnnotationMode();
        });
    };
    reader.readAsDataURL(file);
}

function centerImageInCanvas() {
    if (!currentImage) return;
    
    const img = currentImage;
    const container = document.getElementById('annotationContainer');
    const containerWidth = container.clientWidth - 30; // 减去padding
    const containerHeight = container.clientHeight - 30; // 减去padding
    
    // 更新canvas尺寸以匹配容器
    fabricCanvas.setDimensions({
        width: containerWidth,
        height: containerHeight
    });
    
    const canvasWidth = fabricCanvas.getWidth();
    const canvasHeight = fabricCanvas.getHeight();
    
    // 计算图片和容器的宽高比
    const imgRatio = img.width / img.height;
    const containerRatio = canvasWidth / canvasHeight;
    
    // 根据比例决定缩放方式，确保图片完全显示在容器内且保持原比例
    let scale;
    if (imgRatio > containerRatio) {
        // 图片更宽，以宽度为准缩放
        scale = canvasWidth / img.width;
    } else {
        // 图片更高，以高度为准缩放
        scale = canvasHeight / img.height;
    }
    
    // 应用缩放
    img.set({
        scaleX: scale,
        scaleY: scale
    });
    
    // 居中显示
    img.set({
        left: (canvasWidth - img.getScaledWidth()) / 2,
        top: (canvasHeight - img.getScaledHeight()) / 2,
        selectable: false,
        evented: false
    });
    
    fabricCanvas.add(img);
    fabricCanvas.sendToBack(img);
    // 保证已有标注在图片之上
    fabricCanvas.getObjects().forEach(o => { if (o.isAnnotation) fabricCanvas.bringToFront(o); });
    fabricCanvas.renderAll();
    
    // 更新标注框位置
    updateAnnotationPositions();
}

function enableAnnotationMode() {
    let isDrawing = false;
    let isDrawingSession = false;
    let startX, startY;

    // 清理旧的监听，避免重复注册
    fabricCanvas.off('mouse:down');
    fabricCanvas.off('mouse:move');
    fabricCanvas.off('mouse:up');

    fabricCanvas.on('mouse:down', function(o) {
        // 右键 -> 仅菜单
        if (o.e && (o.e.button === 2 || o.e.which === 3)) {
            const target = fabricCanvas.findTarget(o.e, true);
            if (target && target.isAnnotation) {
                showAnnotationContextMenu(o.e, target);
            }
            return;
        }
        // 点在已有标注上 -> 选择/拖拽，不进入绘制
        const target = fabricCanvas.findTarget(o.e, true);
        if (target && target.isAnnotation) {
            isDrawing = false;
            isDrawingSession = false;
            return;
        }
        if (!selectedClass) {
            showInfoModal('提示', '请先选择要标注的类别');
            return;
        }
        
        // 检查是否在图片边界内
        if (!currentImage) return;
        const pointer = fabricCanvas.getPointer(o.e);
        const imgLeft = currentImage.left;
        const imgTop = currentImage.top;
        const imgRight = imgLeft + currentImage.getScaledWidth();
        const imgBottom = imgTop + currentImage.getScaledHeight();
        
        if (pointer.x < imgLeft || pointer.x > imgRight || pointer.y < imgTop || pointer.y > imgBottom) {
            console.log('绘制起点在图片外，忽略');
            return;
        }
        
        isDrawing = true;
        isDrawingSession = true;
        startX = pointer.x;
        startY = pointer.y;
    });

    fabricCanvas.on('mouse:move', function(o) {
        if (!isDrawing || !isDrawingSession) return;
        const pointer = fabricCanvas.getPointer(o.e);
        
        // 约束绘制区域在图片边界内
        if (!currentImage) return;
        const imgLeft = currentImage.left;
        const imgTop = currentImage.top;
        const imgRight = imgLeft + currentImage.getScaledWidth();
        const imgBottom = imgTop + currentImage.getScaledHeight();
        
        let constrainedX = Math.max(imgLeft, Math.min(imgRight, pointer.x));
        let constrainedY = Math.max(imgTop, Math.min(imgBottom, pointer.y));
        
        const width = Math.abs(constrainedX - startX);
        const height = Math.abs(constrainedY - startY);
        
        fabricCanvas.getObjects().forEach(obj => { if (obj.isTemp) fabricCanvas.remove(obj); });
        const rect = new fabric.Rect({
            left: Math.min(startX, constrainedX),
            top: Math.min(startY, constrainedY),
            width: width,
            height: height,
            fill: 'transparent',
            stroke: '#0d6efd',
            strokeWidth: 2,
            isTemp: true,
            selectable: false,
            evented: false
        });
        fabricCanvas.add(rect);
        fabricCanvas.requestRenderAll();
    });

    fabricCanvas.on('mouse:up', function(o) {
        if (!isDrawing || !isDrawingSession) return;
        isDrawing = false;
        isDrawingSession = false;
        const pointer = fabricCanvas.getPointer(o.e);
        const left = Math.min(startX, pointer.x);
        const top = Math.min(startY, pointer.y);
        const width = Math.abs(pointer.x - startX);
        const height = Math.abs(pointer.y - startY);
        fabricCanvas.getObjects().forEach(obj => { if (obj.isTemp) fabricCanvas.remove(obj); });
        if (width > 10 && height > 10 && currentImage) {
            const imgWidth = currentImage.getScaledWidth();
            const imgHeight = currentImage.getScaledHeight();
            const imgLeft = currentImage.left;
            const imgTop = currentImage.top;
            const centerX = (left + width / 2 - imgLeft) / imgWidth;
            const centerY = (top + height / 2 - imgTop) / imgHeight;
            const normW = width / imgWidth;
            const normH = height / imgHeight;
            const rect = addAnnotationNormalized(selectedClass, centerX, centerY, normW, normH);
            // 保存原始归一化坐标
            if (rect) {
                saveAnnotationOriginalCoords(rect, centerX, centerY, normW, normH);
            }
        }
    });
}

function showAnnotationContextMenu(event, targetObject) {
    console.log('显示标注右键菜单', event, targetObject);
    event.preventDefault();
    event.stopPropagation();
    if (!annotationContextMenu) {
        console.log('标注菜单不存在，重新初始化');
        initAnnotationContextMenu();
    }
    currentAnnotationTarget = targetObject;
    annotationContextMenu.style.display = 'block';
    annotationContextMenu.style.left = event.pageX + 'px';
    annotationContextMenu.style.top = event.pageY + 'px';
    console.log('标注菜单已显示在:', event.pageX, event.pageY);
}

function hideAnnotationContextMenu() { 
    if (annotationContextMenu) {
        console.log('隐藏标注右键菜单');
        annotationContextMenu.style.display = 'none'; 
    }
}

function deleteCurrentAnnotation() {
    console.log('删除当前标注:', currentAnnotationTarget);
    if (currentAnnotationTarget) {
        // 同时删除标注框和标签
        const objects = fabricCanvas.getObjects();
        objects.forEach(obj => {
            if (obj.isAnnotationLabel && obj.annotationId === currentAnnotationTarget.annotationId) {
                fabricCanvas.remove(obj);
            }
        });
        fabricCanvas.remove(currentAnnotationTarget);
        fabricCanvas.requestRenderAll();
    }
    hideAnnotationContextMenu();
}

// 通用：按归一化坐标添加标注框
function addAnnotationNormalized(className, xCenter, yCenter, boxWidth, boxHeight) {
    if (!currentImage) return null;
    
    const imgWidth = currentImage.getScaledWidth();
    const imgHeight = currentImage.getScaledHeight();
    const imgLeft = currentImage.left;
    const imgTop = currentImage.top;
    
    let left = imgLeft + (xCenter - boxWidth / 2) * imgWidth;
    let top = imgTop + (yCenter - boxHeight / 2) * imgHeight;
    let width = boxWidth * imgWidth;
    let height = boxHeight * imgHeight;
    
    // 约束在图片范围内
    if (left < imgLeft) { width -= (imgLeft - left); left = imgLeft; }
    if (top < imgTop) { height -= (imgTop - top); top = imgTop; }
    const maxRight = imgLeft + imgWidth;
    const maxBottom = imgTop + imgHeight;
    if (left + width > maxRight) { width = maxRight - left; }
    if (top + height > maxBottom) { height = maxBottom - top; }
    
    // 生成唯一ID
    const annotationId = 'ann_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    const rect = new fabric.Rect({
        left, top, width, height,
        fill: 'transparent', stroke: '#007bff', strokeWidth: 2,
        selectable: true, evented: true,
        className: className, isAnnotation: true, annotationId: annotationId,
        cornerSize: 8, cornerColor: '#007bff', cornerStrokeColor: '#ffffff',
        borderColor: '#007bff', borderScaleFactor: 2, transparentCorners: false,
        lockRotation: true
    });
    
    // 添加类别标签（可超出图片边界）
    const label = new fabric.Text(className, {
        left: left + width / 2, // 水平居中
        top: Math.max(top - 25, imgTop - 25), // 标签位置在标注框上方
        fontSize: 16, // 更大的字体
        fontWeight: 'bold', // 加粗
        fill: '#ffffff', // 白色文字
        backgroundColor: '#007bff', // 蓝色背景
        padding: 4, // 更大的内边距
        selectable: false,
        evented: false,
        isAnnotationLabel: true,
        annotationId: annotationId,
        originX: 'center', // 水平居中对齐
        originY: 'bottom'  // 垂直底部对齐
    });
    
    // 添加边界约束事件 - 使用更简单的方法
    rect.on('moving', function(e) {
        const obj = e.target;
        if (!obj || !currentImage) return;
        
        const imgLeft = currentImage.left;
        const imgTop = currentImage.top;
        const imgWidth = currentImage.getScaledWidth();
        const imgHeight = currentImage.getScaledHeight();
        // 使用缩放后的尺寸进行边界约束
        const objW = obj.getScaledWidth();
        const objH = obj.getScaledHeight();

        const newLeft = Math.max(imgLeft, Math.min(imgLeft + imgWidth - objW, obj.left));
        const newTop = Math.max(imgTop, Math.min(imgTop + imgHeight - objH, obj.top));
        obj.set({ left: newLeft, top: newTop });

        // 同步更新标签位置（使用缩放后尺寸）
        fabricCanvas.getObjects().forEach(o => {
            if (o.isAnnotationLabel && o.annotationId === annotationId) {
                o.set({ 
                    left: newLeft + objW / 2,
                    top: Math.max(newTop - 25, imgTop - 25)
                });
            }
        });
    });
    
    rect.on('scaled', function(e) {
        const obj = e.target;
        if (!obj || !currentImage) return;
        
        const imgLeft = currentImage.left;
        const imgTop = currentImage.top;
        const imgWidth = currentImage.getScaledWidth();
        const imgHeight = currentImage.getScaledHeight();
        
        // 确保缩放后不超出边界
        const newWidth = Math.min(obj.width * obj.scaleX, imgWidth);
        const newHeight = Math.min(obj.height * obj.scaleY, imgHeight);
        
        obj.set({ 
            width: newWidth, 
            height: newHeight, 
            scaleX: 1, 
            scaleY: 1 
        });
        
        // 确保不超出边界
        if (obj.left < imgLeft) obj.set({ left: imgLeft });
        if (obj.top < imgTop) obj.set({ top: imgTop });
        if (obj.left + newWidth > imgLeft + imgWidth) obj.set({ left: imgLeft + imgWidth - newWidth });
        if (obj.top + newHeight > imgTop + imgHeight) obj.set({ top: imgTop + imgHeight - newHeight });
        
        // 更新标签位置
        // 更新标签位置（use newWidth/newHeight for centering）
        fabricCanvas.getObjects().forEach(o => {
            if (o.isAnnotationLabel && o.annotationId === annotationId) {
                o.set({ 
                    left: obj.left + newWidth / 2,
                    top: Math.max(obj.top - 25, imgTop - 25)
                });
            }
        });
    });

    // 在对象修改结束时（例如拖拽或缩放后）确保标签位置正确
    rect.on('modified', function(e) {
        const obj = e.target;
        if (!obj || !currentImage) return;
        const imgLeft = currentImage.left;
        const imgTop = currentImage.top;
        const objW = obj.getScaledWidth();
        const objH = obj.getScaledHeight();
        fabricCanvas.getObjects().forEach(o => {
            if (o.isAnnotationLabel && o.annotationId === annotationId) {
                o.set({ left: obj.left + objW / 2, top: Math.max(obj.top - 25, imgTop - 25) });
            }
        });
    });
    
    // 右键菜单事件 - 使用更简单的方法
    rect.on('mousedown', function(evt) {
        console.log('标注框鼠标按下:', evt.e.button, evt.e.which, evt.e.type);
        // 检测右键：button 2 或 which 3
        if (evt.e && (evt.e.button === 2 || evt.e.which === 3)) {
            console.log('右键点击标注框，显示菜单');
            evt.e.preventDefault();
            evt.e.stopPropagation();
            showAnnotationContextMenu(evt.e, rect);
            return false; // 阻止默认行为
        }
    });
    
    // 添加到画布
    fabricCanvas.add(rect);
    fabricCanvas.add(label);
    fabricCanvas.bringToFront(rect);
    fabricCanvas.bringToFront(label);
    fabricCanvas.setActiveObject(rect);
    fabricCanvas.requestRenderAll();
    
    console.log('添加标注框:', className, '位置:', left, top, '尺寸:', width, height);
    return rect;
}

// 更新所有标注框位置（窗口缩放后调用）
function updateAnnotationPositions() {
    if (!currentImage) return;
    
    console.log('更新标注框位置');
    const imgWidth = currentImage.getScaledWidth();
    const imgHeight = currentImage.getScaledHeight();
    const imgLeft = currentImage.left;
    const imgTop = currentImage.top;
    
    fabricCanvas.getObjects().forEach(obj => {
        if (obj.isAnnotation && obj.originalCoords) {
            const coords = obj.originalCoords;
            const left = imgLeft + (coords.xCenter - coords.boxWidth / 2) * imgWidth;
            const top = imgTop + (coords.yCenter - coords.boxHeight / 2) * imgHeight;
            const width = coords.boxWidth * imgWidth;
            const height = coords.boxHeight * imgHeight;
            
            obj.set({ left, top, width, height });
            
            // 更新标签位置
            fabricCanvas.getObjects().forEach(label => {
                if (label.isAnnotationLabel && label.annotationId === obj.annotationId) {
                    label.set({ 
                        left: left + width / 2, // 水平居中
                        top: Math.max(top - 25, imgTop - 25) // 与创建时保持一致
                    });
                }
            });
        }
    });
    
    fabricCanvas.requestRenderAll();
}

// 保存标注框的原始归一化坐标
function saveAnnotationOriginalCoords(rect, xCenter, yCenter, boxWidth, boxHeight) {
    rect.originalCoords = { xCenter, yCenter, boxWidth, boxHeight };
}

function clearAnnotations() {
    if (currentImage) {
        fabricCanvas.clear();
        fabricCanvas.add(currentImage);
        fabricCanvas.sendToBack(currentImage);
        fabricCanvas.renderAll();
    }
}

function submitSample() {
    if (!currentImage) {
        showInfoModal('提示', '请先上传图片');
        return;
    }
    
    if (!selectedClass) {
        showInfoModal('提示', '请选择目标类别');
        return;
    }
    
    // 收集标注信息（归一化坐标）
    const labels = [];
    fabricCanvas.getObjects().forEach(obj => {
        if (obj.isAnnotation && obj.className) {
            // 获取图片的实际尺寸（不是画布尺寸）
            const imgWidth = currentImage.width; // 原始图片宽度
            const imgHeight = currentImage.height; // 原始图片高度
            
            // 获取标注框相对于图片的位置和尺寸
            const imgLeft = currentImage.left;
            const imgTop = currentImage.top;
            const imgScaledWidth = currentImage.getScaledWidth();
            const imgScaledHeight = currentImage.getScaledHeight();
            
            // 计算标注框在图片中的实际像素位置
            const objLeft = obj.left - imgLeft;
            const objTop = obj.top - imgTop;
            const objWidth = obj.width;
            const objHeight = obj.height;
            
            // 转换为归一化坐标 (0-1)
            const centerX = (objLeft + objWidth / 2) / imgScaledWidth;
            const centerY = (objTop + objHeight / 2) / imgScaledHeight;
            const normWidth = objWidth / imgScaledWidth;
            const normHeight = objHeight / imgScaledHeight;
            
            console.log('标注框信息:', {
                className: obj.className,
                objLeft, objTop, objWidth, objHeight,
                imgScaledWidth, imgScaledHeight,
                centerX, centerY, normWidth, normHeight
            });
            
            labels.push({ 
                class_name: obj.className, 
                x: centerX, 
                y: centerY, 
                width: normWidth, 
                height: normHeight 
            });
        }
    });
    
    // 准备提交数据
    const formData = new FormData();

    // 重要：优先使用原始图片文件（currentImageFile）提交
    if (currentImageFile) {
        formData.append('image', currentImageFile, currentFileName);
        // 直接提交
        submitSampleData(formData, null, labels);
        return;
    }

    // 如果没有原始文件，需要从原始图片元素获取 natural size
    // 创建一个临时的纯图片canvas
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');

    // 使用图片元素的 naturalWidth / naturalHeight 或 currentImage.width/currentImage.height
    const imgEl = currentImage.getElement ? currentImage.getElement() : null;
    const naturalW = imgEl && imgEl.naturalWidth ? imgEl.naturalWidth : (currentImage.width || currentImage.getScaledWidth());
    const naturalH = imgEl && imgEl.naturalHeight ? imgEl.naturalHeight : (currentImage.height || currentImage.getScaledHeight());

    tempCanvas.width = naturalW;
    tempCanvas.height = naturalH;

    // 将图片绘制到临时canvas（不包含标注框），绘制原始像素尺寸
    if (imgEl) {
        tempCtx.drawImage(imgEl, 0, 0, naturalW, naturalH);
    } else {
        // 退回到使用fabric对象的 element
        tempCtx.drawImage(currentImage.getElement(), 0, 0, naturalW, naturalH);
    }

    // 转换为blob并提交
    tempCanvas.toBlob(function(blob) {
        if (blob) {
            formData.append('image', blob, currentFileName || ('image_' + Date.now() + '.jpg'));
        }
        submitSampleData(formData, null, labels);
    }, 'image/jpeg', 0.9);
}

// 提交样本数据的辅助函数
function submitSampleData(formData, blob, labels) {
    // 如果传入了blob并且尚未在formData中包含'image'，则追加
    try {
        const hasImage = Array.from(formData.keys()).indexOf('image') !== -1;
        if (blob && blob instanceof Blob && !hasImage) {
            formData.append('image', blob, currentFileName || ('image_' + Date.now() + '.jpg'));
        }
        if (!hasImage && !blob && currentImageFile) {
            // 如果之前没有添加文件，但有原始文件，则添加
            formData.append('image', currentImageFile, currentFileName);
        }
    } catch (err) {
        console.error('FormData image append error:', err);
    }

    if (currentFileName) formData.append('file_name', currentFileName);
    formData.append('labels', JSON.stringify(labels));
    formData.append('target_classes', JSON.stringify([selectedClass]));
    
    console.log('提交样本数据:', { labels, target_classes: [selectedClass] });
    
    fetch('/api/sample/submit', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showInfoModal('提示', '样本提交成功');
            loadClasses(); // 刷新类别统计
        } else {
            showInfoModal('错误', '提交失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('提交样本错误:', error);
        showInfoModal('错误', '提交样本失败');
    });
}

function getScreenshot() {
    fetch('/api/sample/get-screenshot')
    .then(response => response.json())
    .then(data => {
        if (data.image_url) {
            fabric.Image.fromURL(data.image_url, function(img) {
                fabricCanvas.clear();
                
                const canvasWidth = fabricCanvas.getWidth();
                const canvasHeight = fabricCanvas.getHeight();
                const imgRatio = img.width / img.height;
                const canvasRatio = canvasWidth / canvasHeight;
                
                if (imgRatio > canvasRatio) {
                    img.scaleToWidth(canvasWidth);
                } else {
                    img.scaleToHeight(canvasHeight);
                }
                
                img.set({
                    left: (canvasWidth - img.getScaledWidth()) / 2,
                    top: (canvasHeight - img.getScaledHeight()) / 2,
                    selectable: false,
                    evented: false
                });
                
                fabricCanvas.add(img);
                fabricCanvas.sendToBack(img);
                currentImage = img;
                currentFileName = data.file_name;
                
                document.getElementById('dropHint').style.display = 'none';
                enableAnnotationMode();
            });
        }
    })
    .catch(error => {
        console.error('获取截图错误:', error);
        alert('获取截图失败');
    });
}

// 模型训练功能
function loadModels() {
    fetch('/api/models')
    .then(response => response.json())
    .then(data => {
        const select = document.getElementById('modelSelect');
        select.innerHTML = '<option value="">选择模型</option>';
        
        if (data.models && Array.isArray(data.models)) {
            data.models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = `${model.name} (${model.classes ? model.classes.join(', ') : ''})`;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('加载模型错误:', error);
    });
}

function startTraining() {
    if (!selectedClass) {
        showInfoModal('提示', '请选择要训练的类别');
        return;
    }
    
    // 显示训练配置模态框
    const modal = new bootstrap.Modal(document.getElementById('trainConfigModal'));
    modal.show();
}

function confirmTraining() {
    const modelName = document.getElementById('trainModelName').value.trim();
    const baseModel = document.getElementById('baseModelSelect').value;
    const epochs = parseInt(document.getElementById('epochs').value);
    const batchSize = parseInt(document.getElementById('batchSize').value);
    const learningRate = parseFloat(document.getElementById('learningRate').value);
    const patience = parseInt(document.getElementById('patience').value);
    
    if (!modelName) {
        showInfoModal('提示', '请输入模型名称');
        return;
    }
    
    const trainParams = {
        epochs: epochs,
        batch: batchSize,
        lr0: learningRate,
        patience: patience
    };
    
    fetch('/api/train/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model_name: modelName,
            base_model: baseModel,
            target_classes: [selectedClass],
            train_params: trainParams
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showInfoModal('提示', '训练已开始，请查看日志');
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('trainConfigModal')).hide();
            // 清空日志区域
            document.getElementById('trainingLog').innerHTML = '';
        } else {
            showInfoModal('错误', '训练启动失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('启动训练错误:', error);
        showInfoModal('错误', '启动训练失败');
    });
}

// 模型推理功能
function manualInfer() {
    if (!currentImage) {
        showInfoModal('提示', '请先上传图片');
        return;
    }
    
    const modelName = document.getElementById('modelSelect').value;
    if (!modelName) {
        showInfoModal('提示', '请选择模型');
        return;
    }
    
    const confThreshold = parseFloat(document.getElementById('confThreshold').value);
    
    // 将当前图片转换为blob进行推理
    fabricCanvas.toBlob(function(blob) {
        const formData = new FormData();
        formData.append('image', blob, 'inference.jpg');
        formData.append('model_name', modelName);
        formData.append('confidence', confThreshold);
        
        fetch('/api/infer', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.results) {
                displayInferenceResults(data.results, data.inference_time);
            } else {
                showInfoModal('错误', '推理失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('推理错误:', error);
            showInfoModal('错误', '推理失败');
        });
    });
}

function displayInferenceResults(results, inferenceTime) {
    const resultArea = document.getElementById('resultArea');
    resultArea.innerHTML = `
        <div class="mb-2">
            <small class="text-muted">推理耗时: ${Number(inferenceTime).toFixed(3)}s</small>
        </div>
    `;
    
    if (results.length === 0) {
        resultArea.innerHTML += '<div class="text-muted">未检测到目标</div>';
        return;
    }
    
    results.forEach((result, index) => {
        const resultItem = document.createElement('div');
        resultItem.className = 'border rounded p-2 mb-2';
        resultItem.innerHTML = `
            <div class="d-flex justify-content-between">
                <span>${result.class_name}</span>
                <span class="badge bg-primary">${(result.confidence * 100).toFixed(1)}%</span>
            </div>
        `;
        resultArea.appendChild(resultItem);
    });
    
    // 在画布上显示检测框
    showDetectionBoxes(results);
}

function showDetectionBoxes(results) {
    // 移除之前的检测框
    const objects = fabricCanvas.getObjects();
    objects.forEach(obj => {
        if (obj.isDetection) {
            fabricCanvas.remove(obj);
        }
    });
    
    // 添加新的检测框
    results.forEach(result => {
        const imgWidth = currentImage.getScaledWidth();
        const imgHeight = currentImage.getScaledHeight();
        const imgLeft = currentImage.left;
        const imgTop = currentImage.top;
        
        const xc = result.x; // 归一化中心
        const yc = result.y;
        const w = result.width;
        const h = result.height;
        const left = imgLeft + (xc - w / 2) * imgWidth;
        const top = imgTop + (yc - h / 2) * imgHeight;
        const width = w * imgWidth;
        const height = h * imgHeight;
        
        const rect = new fabric.Rect({
            left: left,
            top: top,
            width: width,
            height: height,
            fill: 'transparent',
            stroke: '#dc3545',
            strokeWidth: 3,
            isDetection: true,
            selectable: false
        });
        
        const text = new fabric.Text(`${result.class_name} ${(result.confidence * 100).toFixed(1)}%`, {
            left: left,
            top: top - 25,
            fontSize: 12,
            fill: '#dc3545',
            backgroundColor: 'white',
            isDetection: true,
            selectable: false
        });
        
        fabricCanvas.add(rect);
        fabricCanvas.add(text);
    });
    
    fabricCanvas.renderAll();
}

function updateSelectionControls(obj) {
    if (obj && obj.className) {
        console.log('选中标注:', obj.className);
        
        // 优化控制点显示
        obj.set({
            cornerSize: 8,
            cornerColor: '#007bff',
            cornerStrokeColor: '#ffffff',
            borderColor: '#007bff',
            borderScaleFactor: 2,
            transparentCorners: false
        });
        
        fabricCanvas.renderAll();
    }
}

function updateAnnotationInfo(obj) {
    if (obj && obj.className) {
        console.log('选中标注:', obj.className);
    }
}