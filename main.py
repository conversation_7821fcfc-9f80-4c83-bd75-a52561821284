#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO训练平台 - FastAPI后端服务
集成数据标注、模型训练、推理识别的一体化平台
"""

import os
import json
import asyncio
import shutil
import yaml
import cv2
import logging
from pathlib import Path
import sys
import subprocess
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, UploadFile, File, WebSocket, WebSocketDisconnect, Form, Body
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="YOLO训练平台",
    description="集数据标注与YOLO训练一体的Web应用",
    version="1.0.0"
)

# 全局变量
training_process = None

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/data", StaticFiles(directory="data"), name="data")
app.mount("/models", StaticFiles(directory="models"), name="models")
app.mount("/app_screenshots", StaticFiles(directory="app_screenshots"), name="screenshots")

# 全局变量
connected_websockets: List[WebSocket] = []
training_process = None

# 数据模型
class CategoryCreate(BaseModel):
    name: str
    description: str = ""

class SampleSubmit(BaseModel):
    category: str
    image_path: str
    annotations: List[Dict[str, Any]]
    is_negative: bool = False

class TrainingConfig(BaseModel):
    categories: List[str]
    epochs: int = 100
    batch_size: int = 16
    img_size: int = 640
    patience: int = 50
    save_period: int = 10

class InferenceRequest(BaseModel):
    image_path: str
    confidence: float = 0.25
    model_name: str = "best.pt"

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")

    async def broadcast(self, message: str):
        """广播消息到所有连接的客户端"""
        if self.active_connections:
            for connection in self.active_connections.copy():
                try:
                    await connection.send_text(message)
                except:
                    self.disconnect(connection)

manager = ConnectionManager()

# 工具函数
def ensure_dir(path: str) -> None:
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)

def load_class_order() -> List[str]:
    order_file = Path("data/classes/_order.json")
    if order_file.exists():
        try:
            return json.loads(order_file.read_text(encoding="utf-8"))
        except Exception:
            return []
    return []

def save_class_order(order: List[str]) -> None:
    ensure_dir("data/classes")
    order_file = Path("data/classes/_order.json")
    order_file.write_text(json.dumps(order, ensure_ascii=False, indent=2), encoding="utf-8")

def get_timestamp() -> str:
    """获取时间戳字符串"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

# 启动时初始化
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    logger.info("YOLO训练平台启动中...")
    
    # 确保必要目录存在
    dirs = [
        "data/common_images", "data/classes", "models", 
        "train_data", "app_screenshots", "logs"
    ]
    for dir_path in dirs:
        ensure_dir(dir_path)
    
    # 执行孤儿文件扫描修复
    await scan_and_fix_orphan_files()
    
    logger.info("YOLO训练平台启动完成")

# 基础路由
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页面"""
    try:
        with open("index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="主页面文件未找到")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# WebSocket端点
@app.websocket("/ws/logs")
async def websocket_logs(websocket: WebSocket):
    """WebSocket日志传输端点"""
    await manager.connect(websocket)
    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# 兼容前端：训练日志专用WS路径
@app.websocket("/ws/train/log")
async def websocket_train_log(websocket: WebSocket):
    await websocket_logs(websocket)

# 类别管理API
# 旧接口 - 仍保留
@app.post("/api/categories")
async def create_category(category: CategoryCreate):
    """创建新类别"""
    try:
        category_dir = f"data/classes/{category.name}"
        ensure_dir(category_dir)
        
        # 创建类别配置文件
        config = {
            "name": category.name,
            "description": category.description,
            "created_at": datetime.now().isoformat(),
            "sample_count": 0
        }
        
        config_path = f"{category_dir}/config.json"
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"类别创建成功: {category.name}")
        return {"success": True, "message": f"类别 '{category.name}' 创建成功"}
        
    except Exception as e:
        logger.error(f"创建类别失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建类别失败: {str(e)}")

# 旧接口 - 仍保留
@app.get("/api/categories")
async def get_categories():
    """获取所有类别"""
    try:
        categories = []
        classes_dir = Path("data/classes")
        
        if classes_dir.exists():
            for category_dir in classes_dir.iterdir():
                if category_dir.is_dir():
                    config_path = category_dir / "config.json"
                    if config_path.exists():
                        with open(config_path, "r", encoding="utf-8") as f:
                            config = json.load(f)
                        
                        # 统计样本数量
                        sample_count = len(list(category_dir.glob("*.txt")))
                        config["sample_count"] = sample_count
                        categories.append(config)
                    else:
                        # 兼容没有配置文件的旧目录
                        categories.append({
                            "name": category_dir.name,
                            "description": "",
                            "created_at": "",
                            "sample_count": len(list(category_dir.glob("*.txt")))
                        })
        
        # 应用自定义排序
        custom_order = load_class_order()
        if custom_order:
            order_map = {name: i for i, name in enumerate(custom_order)}
            categories.sort(key=lambda c: order_map.get(c.get("name",""), 10**9))
        return {"categories": categories}
        
    except Exception as e:
        logger.error(f"获取类别列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取类别列表失败: {str(e)}")

# 旧接口 - 仍保留
@app.delete("/api/categories/{category_name}")
async def delete_category(category_name: str):
    """删除类别"""
    try:
        import shutil
        category_dir = Path(f"data/classes/{category_name}")
        
        if not category_dir.exists():
            raise HTTPException(status_code=404, detail=f"类别 '{category_name}' 不存在")
        
        # 删除类别目录及所有内容
        shutil.rmtree(category_dir)
        
        logger.info(f"类别删除成功: {category_name}")
        return {"success": True, "message": f"类别 '{category_name}' 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除类别失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除类别失败: {str(e)}")

# 新接口 - 与前端对齐
@app.post("/api/class/create")
async def api_class_create(payload: Dict[str, Any] = Body(...)):
    name = payload.get("class_name", "").strip()
    if not name:
        raise HTTPException(status_code=400, detail="class_name 不能为空")
    # 复用旧实现逻辑
    return await create_category(CategoryCreate(name=name))

@app.get("/api/class/get-all")
async def api_class_get_all():
    raw = await get_categories()
    return raw

@app.delete("/api/class/delete")
async def api_class_delete(payload: Dict[str, Any] = Body(...)):
    name = payload.get("class_name")
    if not name:
        raise HTTPException(status_code=400, detail="class_name 不能为空")
    return await delete_category(name)

# 兼容：部分环境/代理不允许 DELETE 携带 body，这里额外提供 POST 方式
@app.post("/api/class/delete")
async def api_class_delete_post(payload: Dict[str, Any] = Body(...)):
    return await api_class_delete(payload)

@app.post("/api/class/rename")
async def api_class_rename(payload: Dict[str, Any] = Body(...)):
    old_name = payload.get("old_name", "").strip()
    new_name = payload.get("new_name", "").strip()
    if not old_name or not new_name:
        raise HTTPException(status_code=400, detail="old_name/new_name 不能为空")
    old_dir = Path(f"data/classes/{old_name}")
    new_dir = Path(f"data/classes/{new_name}")
    if not old_dir.exists():
        raise HTTPException(status_code=404, detail=f"类别 '{old_name}' 不存在")
    if new_dir.exists():
        raise HTTPException(status_code=400, detail=f"目标类别 '{new_name}' 已存在")
    try:
        old_dir.rename(new_dir)
        # 更新配置文件中的名称
        config_path = new_dir / "config.json"
        if config_path.exists():
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    cfg = json.load(f)
                cfg["name"] = new_name
                with open(config_path, "w", encoding="utf-8") as f:
                    json.dump(cfg, f, ensure_ascii=False, indent=2)
            except Exception:
                pass
        # 更新排序文件中的名称
        try:
            order = load_class_order()
            if old_name in order:
                order = [new_name if n == old_name else n for n in order]
                save_class_order(order)
        except Exception:
            pass
        await update_category_sample_counts()
        return {"success": True}
    except Exception as e:
        logger.error(f"重命名类别失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/class/reorder")
async def api_class_reorder(payload: Dict[str, Any] = Body(...)):
    order = payload.get("order")
    if not isinstance(order, list) or not all(isinstance(x, str) for x in order):
        raise HTTPException(status_code=400, detail="order 必须为字符串数组")
    # 仅保存存在的类别
    valid = []
    classes_dir = Path("data/classes")
    for name in order:
        if (classes_dir / name).exists():
            valid.append(name)
    save_class_order(valid)
    return {"success": True, "order": valid}

# 样本管理API
@app.post("/api/upload-image")
async def upload_image(file: UploadFile = File(...)):
    """上传图片到公共图片库"""
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")
        
        # 生成唯一文件名
        timestamp = get_timestamp()
        file_ext = Path(file.filename).suffix.lower()
        filename = f"{timestamp}_{file.filename}"
        file_path = f"data/common_images/{filename}"
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"图片上传成功: {filename}")
        return {
            "success": True, 
            "filename": filename,
            "path": file_path,
            "message": "图片上传成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"图片上传失败: {str(e)}")

@app.post("/api/samples")
async def submit_sample(sample: SampleSubmit):
    """提交标注样本"""
    try:
        category_dir = Path(f"data/classes/{sample.category}")
        if not category_dir.exists():
            raise HTTPException(status_code=404, detail=f"类别 '{sample.category}' 不存在")
        
        # 生成样本ID
        timestamp = get_timestamp()
        sample_id = f"{sample.category}_{timestamp}"
        
        if sample.is_negative:
            # 负样本：只记录图片路径
            negative_file = category_dir / f"{sample_id}_negative.txt"
            with open(negative_file, "w", encoding="utf-8") as f:
                f.write(f"{sample.image_path}\n")
        else:
            # 正样本：保存YOLO格式标注
            annotation_file = category_dir / f"{sample_id}.txt"
            with open(annotation_file, "w", encoding="utf-8") as f:
                for ann in sample.annotations:
                    # YOLO格式: class_id center_x center_y width height
                    f.write(f"0 {ann['center_x']} {ann['center_y']} {ann['width']} {ann['height']}\n")
        
        # 更新类别配置中的样本计数
        config_path = category_dir / "config.json"
        if config_path.exists():
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            config["sample_count"] = len(list(category_dir.glob("*.txt")))
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"样本提交成功: {sample_id}")
        return {
            "success": True, 
            "sample_id": sample_id,
            "message": "样本提交成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"样本提交失败: {e}")
        raise HTTPException(status_code=500, detail=f"样本提交失败: {str(e)}")

# 新接口：与前端表单提交对齐（multipart）
@app.post("/api/sample/submit")
async def api_sample_submit(
    image: UploadFile = File(...),
    file_name: str = Form(...),
    labels: str = Form("[]"),
    target_classes: str = Form("[]"),
    is_update: Optional[bool] = Form(False)
):
    try:
        # 保存到公共图片库
        ensure_dir("data/common_images")
        # 使用提供的文件名保存
        image_bytes = await image.read()
        save_path = Path("data/common_images") / file_name
        with open(save_path, "wb") as f:
            f.write(image_bytes)

        # 解析标注与类别
        try:
            labels_data = json.loads(labels) if labels else []
        except Exception:
            labels_data = []
        try:
            target_list = json.loads(target_classes) if target_classes else []
        except Exception:
            target_list = []

        # 获取图片尺寸
        img = cv2.imdecode(
            np.frombuffer(image_bytes, dtype=np.uint8), cv2.IMREAD_COLOR
        ) if 'np' in globals() else None
        if img is None:
            import numpy as np  # lazy import
            img = cv2.imdecode(np.frombuffer(image_bytes, dtype=np.uint8), cv2.IMREAD_COLOR)
        img_h, img_w = img.shape[:2]

        # 为每个目标类别写入 labels 或负样本
        for cls in target_list:
            cls_dir = Path(f"data/classes/{cls}")
            (cls_dir / "labels").mkdir(parents=True, exist_ok=True)
            (cls_dir / "negative").mkdir(parents=True, exist_ok=True)

            base_stem = Path(file_name).stem
            label_file = cls_dir / "labels" / f"{base_stem}.txt"

            cls_labels = [l for l in labels_data if l.get("class_name") == cls]
            if cls_labels:
                # 支持两种坐标格式：
                # 1. 新的归一化中心点格式: x, y, width, height
                # 2. 旧的像素坐标格式: x1, y1, x2, y2
                lines = []
                for l in cls_labels:
                    if 'x' in l and 'y' in l and 'width' in l and 'height' in l:
                        # 新格式：已经是归一化坐标
                        cx = l.get('x', 0)
                        cy = l.get('y', 0)
                        w = l.get('width', 0)
                        h = l.get('height', 0)
                    else:
                        # 旧格式：像素坐标转换为归一化坐标
                        x1, y1, x2, y2 = l.get("x1", 0), l.get("y1", 0), l.get("x2", 0), l.get("y2", 0)
                        cx = ((x1 + x2) / 2.0) / img_w
                        cy = ((y1 + y2) / 2.0) / img_h
                        w = (abs(x2 - x1)) / img_w
                        h = (abs(y2 - y1)) / img_h
                    
                    # 暂用 class_id 0（没有类别索引表时）
                    lines.append(f"0 {cx:.6f} {cy:.6f} {w:.6f} {h:.6f}")
                with open(label_file, "w", encoding="utf-8") as f:
                    f.write("\n".join(lines) + "\n")
            else:
                # 负样本：拷贝一份图片到 negative 目录
                dst = cls_dir / "negative" / Path(file_name).name
                try:
                    shutil.copy2(str(save_path), str(dst))
                except Exception:
                    pass

        await update_category_sample_counts()
        return {"success": True, "file_name": file_name}
    except Exception as e:
        logger.error(f"提交样本失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/sample/get-screenshot")
async def api_get_screenshot():
    try:
        shots_dir = Path("app_screenshots")
        ensure_dir(str(shots_dir))
        files = [p for p in shots_dir.glob("*") if p.is_file()]
        if not files:
            return {"image_url": None, "file_name": None}
        # 取最新一张
        files.sort(key=lambda p: p.stat().st_mtime, reverse=True)
        sel = files[0]
        return {"image_url": f"/app_screenshots/{sel.name}", "file_name": sel.name}
    except Exception as e:
        logger.error(f"获取截图失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/images")
async def get_images():
    """获取公共图片库中的所有图片"""
    try:
        images = []
        images_dir = Path("data/common_images")
        
        if images_dir.exists():
            for img_file in images_dir.iterdir():
                if img_file.is_file() and img_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
                    images.append({
                        "filename": img_file.name,
                        "path": str(img_file),
                        "size": img_file.stat().st_size,
                        "modified": datetime.fromtimestamp(img_file.stat().st_mtime).isoformat()
                    })
        
        # 按修改时间倒序排列
        images.sort(key=lambda x: x['modified'], reverse=True)
        return {"images": images}
        
    except Exception as e:
        logger.error(f"获取图片列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片列表失败: {str(e)}")

@app.get("/api/samples/{category_name}")
async def get_samples(category_name: str):
    """获取指定类别的样本列表"""
    try:
        category_dir = Path(f"data/classes/{category_name}")
        if not category_dir.exists():
            raise HTTPException(status_code=404, detail=f"类别 '{category_name}' 不存在")
        
        samples = []
        for sample_file in category_dir.glob("*.txt"):
            sample_info = {
                "filename": sample_file.name,
                "path": str(sample_file),
                "is_negative": "_negative" in sample_file.name,
                "modified": datetime.fromtimestamp(sample_file.stat().st_mtime).isoformat()
            }
            samples.append(sample_info)
        
        # 按修改时间倒序排列
        samples.sort(key=lambda x: x['modified'], reverse=True)
        return {"samples": samples}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取样本列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取样本列表失败: {str(e)}")

# 模型训练API
@app.post("/api/training/start")
async def start_training(config: TrainingConfig):
    """启动模型训练"""
    global training_process
    
    try:
        # 检查是否已有训练在进行
        if training_process and training_process.returncode is None:
            raise HTTPException(status_code=400, detail="已有训练任务在进行中")
        
        # 验证类别是否存在
        for category in config.categories:
            category_dir = Path(f"data/classes/{category}")
            if not category_dir.exists():
                raise HTTPException(status_code=404, detail=f"类别 '{category}' 不存在")
            
            # 检查是否有足够的样本
            sample_files = list(category_dir.glob("*.txt"))
            if len(sample_files) < 5:
                raise HTTPException(status_code=400, detail=f"类别 '{category}' 样本数量不足（至少需要5个）")
        
        # 准备训练数据
        await prepare_training_data(config.categories)
        
        # 创建训练配置文件
        train_config = {
            "categories": config.categories,
            "epochs": config.epochs,
            "batch_size": config.batch_size,
            "img_size": config.img_size,
            "patience": config.patience,
            "save_period": config.save_period,
            "started_at": datetime.now().isoformat()
        }
        
        config_path = "train_data/train_config.json"
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(train_config, f, ensure_ascii=False, indent=2)
        
        # 启动训练任务
        asyncio.create_task(run_training_task(config))
        
        await manager.broadcast(f"训练任务启动: {len(config.categories)}个类别, {config.epochs}轮训练")
        logger.info(f"训练任务启动: {config.categories}")
        
        return {
            "success": True,
            "message": "训练任务已启动",
            "config": train_config
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动训练失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动训练失败: {str(e)}")

# 新接口：与前端对齐
@app.post("/api/train/start")
async def api_train_start(payload: Dict[str, Any] = Body(...)):
    try:
        model_name = payload.get("model_name")
        base_model = payload.get("base_model", "yolov8n.pt")
        target_classes = payload.get("target_classes", [])
        params = payload.get("train_params", {})

        cfg = TrainingConfig(
            categories=target_classes,
            epochs=int(params.get("epochs", 100)),
            batch_size=int(params.get("batch", 8)),
            img_size=640,
            patience=int(params.get("patience", 20)),
            save_period=10,
        )

        # 使用携带的基底模型
        asyncio.create_task(run_training_task_with_base(cfg, base_model))
        await manager.broadcast(f"训练任务启动: {len(cfg.categories)}个类别, {cfg.epochs}轮, 基底模型: {base_model}")
        return {"success": True}
    except Exception as e:
        logger.error(f"/api/train/start 失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/training/status")
async def get_training_status():
    """获取训练状态"""
    try:
        global training_process
        
        status = {
            "is_training": training_process and training_process.returncode is None,
            "process_id": training_process.pid if training_process else None
        }
        
        # 读取训练配置
        config_path = "train_data/train_config.json"
        if Path(config_path).exists():
            with open(config_path, "r", encoding="utf-8") as f:
                status["config"] = json.load(f)
        
        # 读取最新日志
        log_path = "logs/training.log"
        if Path(log_path).exists():
            with open(log_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
                status["recent_logs"] = lines[-10:] if len(lines) > 10 else lines
        
        return status
        
    except Exception as e:
        logger.error(f"获取训练状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取训练状态失败: {str(e)}")

@app.post("/api/training/stop")
async def stop_training():
    """停止训练"""
    global training_process
    
    try:
        if not training_process or training_process.returncode is not None:
            raise HTTPException(status_code=400, detail="没有正在进行的训练任务")
        
        training_process.terminate()
        await asyncio.sleep(2)
        
        if training_process.returncode is None:
            training_process.kill()
        
        await manager.broadcast("训练任务已停止")
        logger.info("训练任务已停止")
        
        return {"success": True, "message": "训练任务已停止"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止训练失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止训练失败: {str(e)}")

@app.get("/api/models")
async def get_models():
    """获取已训练的模型列表"""
    try:
        models = []
        models_dir = Path("models")
        
        if models_dir.exists():
            for model_file in models_dir.glob("*.pt"):
                models.append({
                    "filename": model_file.name,
                    "name": model_file.name,
                    "path": str(model_file),
                    "size": model_file.stat().st_size,
                    "modified": datetime.fromtimestamp(model_file.stat().st_mtime).isoformat(),
                    "classes": []
                })
        
        # 按修改时间倒序排列
        models.sort(key=lambda x: x['modified'], reverse=True)
        return {"models": models}
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

# 训练辅助函数
async def prepare_training_data(categories: List[str]):
    """准备训练数据"""
    train_dir = Path("train_data")
    ensure_dir(str(train_dir / "images"))
    ensure_dir(str(train_dir / "labels"))
    
    # 清理旧的训练数据
    import shutil
    for old_file in train_dir.glob("*"):
        if old_file.is_file():
            old_file.unlink()
        elif old_file.is_dir() and old_file.name in ["images", "labels"]:
            shutil.rmtree(old_file)
            ensure_dir(str(old_file))
    
    # 复制图片和标注文件
    image_count = 0
    for category in categories:
        category_dir = Path(f"data/classes/{category}")
        for sample_file in category_dir.glob("*.txt"):
            if "_negative" not in sample_file.name:
                # 复制标注文件
                shutil.copy2(sample_file, train_dir / "labels")
                
                # 查找对应的图片文件
                sample_name = sample_file.stem
                # 从样本文件中读取图片路径信息或使用命名约定
                image_count += 1
    
    logger.info(f"训练数据准备完成: {image_count}个样本")

async def run_training_task(config: TrainingConfig):
    """运行训练任务"""
    global training_process
    
    try:
        await manager.broadcast("开始准备训练环境...")
        
        # 创建YOLO配置文件
        yaml_config = {
            "path": "train_data",
            "train": "images",
            "val": "images",
            "names": {i: cat for i, cat in enumerate(config.categories)}
        }
        
        yaml_path = "train_data/dataset.yaml"
        import yaml
        with open(yaml_path, "w", encoding="utf-8") as f:
            yaml.dump(yaml_config, f, default_flow_style=False, allow_unicode=True)
        
        await manager.broadcast("开始模型训练...")
        
        # 使用ultralytics进行训练
        from ultralytics import YOLO
        
        # 加载预训练模型（默认）
        model = YOLO("base_models/yolov8n.pt")
        
        # 开始训练
        results = model.train(
            data=yaml_path,
            epochs=config.epochs,
            batch=config.batch_size,
            imgsz=config.img_size,
            patience=config.patience,
            save_period=config.save_period,
            project="models",
            name="train",
            exist_ok=True
        )
        
        await manager.broadcast("训练完成！模型已保存到 models/train/weights/best.pt")
        logger.info("模型训练完成")
        
    except Exception as e:
        error_msg = f"训练过程出错: {str(e)}"
        await manager.broadcast(error_msg)
        logger.error(error_msg)
    finally:
         training_process = None

async def run_training_task_with_base(config: TrainingConfig, base_model_name: str):
    """运行训练任务（可指定基底模型）"""
    try:
        await manager.broadcast("开始准备训练环境...")
        yaml_config = {
            "path": "train_data",
            "train": "images",
            "val": "images",
            "names": {i: cat for i, cat in enumerate(config.categories)}
        }
        yaml_path = "train_data/dataset.yaml"
        with open(yaml_path, "w", encoding="utf-8") as f:
            yaml.dump(yaml_config, f, default_flow_style=False, allow_unicode=True)

        await manager.broadcast("开始模型训练...")
        from ultralytics import YOLO
        model = YOLO(f"base_models/{base_model_name}")
        results = model.train(
            data=yaml_path,
            epochs=config.epochs,
            batch=config.batch_size,
            imgsz=config.img_size,
            patience=config.patience,
            save_period=config.save_period,
            project="models",
            name="train",
            exist_ok=True
        )
        await manager.broadcast("训练完成！模型已保存到 models/train/weights/best.pt")
    except Exception as e:
        await manager.broadcast(f"训练过程出错: {str(e)}")

# 新接口：单张推理（前端FormData）
@app.post("/api/infer")
async def api_infer(model_name: str = Form(...), confidence: float = Form(0.5), image: UploadFile = File(...)):
    try:
        # 保存上传图片至临时位置
        img_bytes = await image.read()
        import numpy as np
        np_img = cv2.imdecode(np.frombuffer(img_bytes, dtype=np.uint8), cv2.IMREAD_COLOR)
        if np_img is None:
            raise HTTPException(status_code=400, detail="无法解析上传图片")
        h, w = np_img.shape[:2]

        # 加载模型
        from ultralytics import YOLO
        model_path = Path("models") / model_name
        if not model_path.exists():
            raise HTTPException(status_code=404, detail=f"模型不存在: {model_name}")
        model = YOLO(str(model_path))

        # 运行推理
        ts0 = datetime.now().timestamp()
        results = model(np_img, conf=confidence)
        ts1 = datetime.now().timestamp()
        inference_time = ts1 - ts0

        parsed = []
        if results and len(results) > 0 and results[0].boxes is not None:
            boxes = results[0].boxes
            for i in range(len(boxes)):
                box = boxes[i]
                x1 = float(box.xyxy[0][0]); y1 = float(box.xyxy[0][1])
                x2 = float(box.xyxy[0][2]); y2 = float(box.xyxy[0][3])
                xc = ((x1 + x2) / 2.0) / w
                yc = ((y1 + y2) / 2.0) / h
                bw = (abs(x2 - x1)) / w
                bh = (abs(y2 - y1)) / h
                cls_id = int(box.cls[0])
                parsed.append({
                    "class_id": cls_id,
                    "class_name": model.names.get(cls_id, str(cls_id)) if hasattr(model, 'names') else str(cls_id),
                    "confidence": float(box.conf[0]),
                    "x": xc, "y": yc, "width": bw, "height": bh
                })

        return {"success": True, "results": parsed, "inference_time": inference_time}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"推理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 模型推理API
@app.post("/api/inference")
async def run_inference(request: InferenceRequest):
    """运行模型推理"""
    try:
        # 验证模型文件是否存在
        model_path = Path(request.model_path)
        if not model_path.exists():
            raise HTTPException(status_code=404, detail=f"模型文件不存在: {request.model_path}")
        
        # 验证图片文件是否存在
        image_path = Path(request.image_path)
        if not image_path.exists():
            raise HTTPException(status_code=404, detail=f"图片文件不存在: {request.image_path}")
        
        # 加载模型并进行推理
        from ultralytics import YOLO
        model = YOLO(str(model_path))
        
        # 运行推理
        results = model(str(image_path), conf=request.confidence)
        
        # 解析推理结果
        detections = []
        if results and len(results) > 0:
            result = results[0]
            if result.boxes is not None:
                boxes = result.boxes
                for i in range(len(boxes)):
                    box = boxes[i]
                    detection = {
                        "class_id": int(box.cls[0]),
                        "class_name": model.names[int(box.cls[0])],
                        "confidence": float(box.conf[0]),
                        "bbox": {
                            "x1": float(box.xyxy[0][0]),
                            "y1": float(box.xyxy[0][1]),
                            "x2": float(box.xyxy[0][2]),
                            "y2": float(box.xyxy[0][3])
                        }
                    }
                    detections.append(detection)
        
        # 保存推理结果截图
        if request.save_result and detections:
            timestamp = get_timestamp()
            result_image_path = f"app_screenshots/inference_{timestamp}.jpg"
            
            # 绘制检测框并保存
            annotated_image = result.plot()
            import cv2
            cv2.imwrite(result_image_path, annotated_image)
            
            logger.info(f"推理结果已保存: {result_image_path}")
        
        return {
            "success": True,
            "detections": detections,
            "total_detections": len(detections),
            "image_path": str(image_path),
            "model_path": str(model_path),
            "confidence_threshold": request.confidence,
            "result_image_path": result_image_path if request.save_result and detections else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"推理失败: {e}")
        raise HTTPException(status_code=500, detail=f"推理失败: {str(e)}")

@app.post("/api/inference/batch")
async def run_batch_inference(model_path: str = Form(...), confidence: float = Form(0.5), images: List[UploadFile] = File(...)):
    """批量推理"""
    try:
        # 验证模型文件
        model_file = Path(model_path)
        if not model_file.exists():
            raise HTTPException(status_code=404, detail=f"模型文件不存在: {model_path}")
        
        # 加载模型
        from ultralytics import YOLO
        model = YOLO(str(model_file))
        
        results = []
        timestamp = get_timestamp()
        
        for i, image_file in enumerate(images):
            try:
                # 保存上传的图片
                temp_image_path = f"data/common_images/batch_{timestamp}_{i}_{image_file.filename}"
                with open(temp_image_path, "wb") as f:
                    content = await image_file.read()
                    f.write(content)
                
                # 运行推理
                inference_results = model(temp_image_path, conf=confidence)
                
                # 解析结果
                detections = []
                if inference_results and len(inference_results) > 0:
                    result = inference_results[0]
                    if result.boxes is not None:
                        boxes = result.boxes
                        for j in range(len(boxes)):
                            box = boxes[j]
                            detection = {
                                "class_id": int(box.cls[0]),
                                "class_name": model.names[int(box.cls[0])],
                                "confidence": float(box.conf[0]),
                                "bbox": {
                                    "x1": float(box.xyxy[0][0]),
                                    "y1": float(box.xyxy[0][1]),
                                    "x2": float(box.xyxy[0][2]),
                                    "y2": float(box.xyxy[0][3])
                                }
                            }
                            detections.append(detection)
                
                results.append({
                    "filename": image_file.filename,
                    "image_path": temp_image_path,
                    "detections": detections,
                    "total_detections": len(detections)
                })
                
            except Exception as e:
                results.append({
                    "filename": image_file.filename,
                    "error": str(e),
                    "detections": [],
                    "total_detections": 0
                })
        
        return {
            "success": True,
            "results": results,
            "total_images": len(images),
            "model_path": model_path,
            "confidence_threshold": confidence
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量推理失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量推理失败: {str(e)}")

# 孤儿文件扫描修复功能
async def scan_and_fix_orphan_files():
    """扫描并修复孤儿文件"""
    try:
        logger.info("开始扫描孤儿文件...")
        
        orphan_images = []
        orphan_labels = []
        fixed_count = 0
        
        # 扫描公共图片库中的孤儿图片
        common_images_dir = Path("data/common_images")
        if common_images_dir.exists():
            for image_file in common_images_dir.glob("*"):
                if image_file.is_file() and image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                    # 检查是否被任何类别引用
                    is_referenced = False
                    for category_dir in Path("data/classes").glob("*"):
                        if category_dir.is_dir():
                            # 检查正样本引用
                            for label_file in category_dir.glob("*.txt"):
                                if "_negative" not in label_file.name:
                                    try:
                                        with open(label_file, "r", encoding="utf-8") as f:
                                            content = f.read()
                                            if str(image_file) in content or image_file.name in content:
                                                is_referenced = True
                                                break
                                    except:
                                        continue
                            
                            # 检查负样本引用
                            negative_file = category_dir / "negative_samples.txt"
                            if negative_file.exists():
                                try:
                                    with open(negative_file, "r", encoding="utf-8") as f:
                                        content = f.read()
                                        if str(image_file) in content or image_file.name in content:
                                            is_referenced = True
                                            break
                                except:
                                    continue
                        
                        if is_referenced:
                            break
                    
                    if not is_referenced:
                        orphan_images.append(image_file)
        
        # 扫描类别目录中的孤儿标注文件
        for category_dir in Path("data/classes").glob("*"):
            if category_dir.is_dir():
                for label_file in category_dir.glob("*.txt"):
                    if "_negative" not in label_file.name and label_file.name != "negative_samples.txt":
                        # 检查对应的图片是否存在
                        image_referenced = False
                        try:
                            with open(label_file, "r", encoding="utf-8") as f:
                                lines = f.readlines()
                                if len(lines) > 0:
                                    # 假设第一行包含图片路径信息
                                    first_line = lines[0].strip()
                                    # 检查是否为YOLO格式的标注（数字开头）
                                    if not first_line.split()[0].replace('.', '').isdigit():
                                        # 可能包含图片路径
                                        for line in lines:
                                            if any(ext in line for ext in ['.jpg', '.jpeg', '.png', '.bmp']):
                                                image_path = line.strip()
                                                if Path(image_path).exists():
                                                    image_referenced = True
                                                    break
                                    else:
                                        # YOLO格式，检查是否有对应的图片文件
                                        possible_image_name = label_file.stem
                                        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                                            possible_image_path = common_images_dir / f"{possible_image_name}{ext}"
                                            if possible_image_path.exists():
                                                image_referenced = True
                                                break
                        except:
                            pass
                        
                        if not image_referenced:
                            orphan_labels.append(label_file)
        
        # 修复孤儿文件
        if orphan_images:
            logger.info(f"发现 {len(orphan_images)} 个孤儿图片文件")
            orphan_dir = Path("data/orphan_files")
            ensure_dir(str(orphan_dir))
            
            for orphan_image in orphan_images:
                try:
                    new_path = orphan_dir / orphan_image.name
                    shutil.move(str(orphan_image), str(new_path))
                    fixed_count += 1
                    logger.info(f"移动孤儿图片: {orphan_image.name} -> {new_path}")
                except Exception as e:
                    logger.error(f"移动孤儿图片失败 {orphan_image}: {e}")
        
        if orphan_labels:
            logger.info(f"发现 {len(orphan_labels)} 个孤儿标注文件")
            for orphan_label in orphan_labels:
                try:
                    orphan_label.unlink()
                    fixed_count += 1
                    logger.info(f"删除孤儿标注文件: {orphan_label}")
                except Exception as e:
                    logger.error(f"删除孤儿标注文件失败 {orphan_label}: {e}")
        
        # 更新类别配置中的样本计数
        await update_category_sample_counts()
        
        if fixed_count > 0:
            logger.info(f"孤儿文件修复完成，共处理 {fixed_count} 个文件")
        else:
            logger.info("未发现孤儿文件")
            
    except Exception as e:
        logger.error(f"孤儿文件扫描修复失败: {e}")

async def update_category_sample_counts():
    """更新所有类别的样本计数"""
    try:
        for category_dir in Path("data/classes").glob("*"):
            if category_dir.is_dir():
                config_file = category_dir / "config.json"
                if config_file.exists():
                    # 统计正样本数量
                    positive_count = len([f for f in category_dir.glob("*.txt") 
                                        if "_negative" not in f.name and f.name != "negative_samples.txt"])
                    
                    # 统计负样本数量
                    negative_count = 0
                    negative_file = category_dir / "negative_samples.txt"
                    if negative_file.exists():
                        try:
                            with open(negative_file, "r", encoding="utf-8") as f:
                                negative_count = len([line for line in f.readlines() if line.strip()])
                        except:
                            negative_count = 0
                    
                    # 更新配置文件
                    try:
                        with open(config_file, "r", encoding="utf-8") as f:
                            config = json.load(f)
                        
                        config["positive_samples"] = positive_count
                        config["negative_samples"] = negative_count
                        config["total_samples"] = positive_count + negative_count
                        config["updated_at"] = datetime.now().isoformat()
                        
                        with open(config_file, "w", encoding="utf-8") as f:
                            json.dump(config, f, ensure_ascii=False, indent=2)
                        
                        logger.info(f"更新类别 {category_dir.name} 样本计数: 正样本={positive_count}, 负样本={negative_count}")
                    except Exception as e:
                        logger.error(f"更新类别配置失败 {category_dir.name}: {e}")
    except Exception as e:
        logger.error(f"更新样本计数失败: {e}")

@app.get("/api/orphan-files/scan")
async def manual_scan_orphan_files():
    """手动触发孤儿文件扫描"""
    try:
        await scan_and_fix_orphan_files()
        return {"success": True, "message": "孤儿文件扫描完成"}
    except Exception as e:
        logger.error(f"手动扫描孤儿文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"扫描失败: {str(e)}")

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"error": "资源未找到", "path": str(request.url.path)}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"内部服务器错误: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误"}
    )

if __name__ == "__main__":
    # CLI: python main.py start|stop
    def _pid_file() -> Path:
        return Path("server.pid")

    def _is_running(pid: int) -> bool:
        try:
            os.kill(pid, 0)
            return True
        except Exception:
            return False

    if len(sys.argv) > 1:
        cmd = sys.argv[1].lower()
        if cmd == "start":
            pid_path = _pid_file()
            if pid_path.exists():
                try:
                    pid = int(pid_path.read_text().strip())
                    if _is_running(pid):
                        print(f"Service already running with PID {pid}")
                        sys.exit(0)
                except Exception:
                    pass
                # 清理无效 pid 文件
                try:
                    pid_path.unlink()
                except Exception:
                    pass

            # 后台启动 uvicorn（不使用 reload，避免频繁重载）
            out = open("server.out", "ab")
            err = open("server.err", "ab")
            proc = subprocess.Popen(
                [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "info"],
                stdout=out,
                stderr=err,
                cwd=str(Path(__file__).parent)
            )
            pid_path.write_text(str(proc.pid))
            print(proc.pid)
            sys.exit(0)

        elif cmd == "stop":
            pid_path = _pid_file()
            if pid_path.exists():
                try:
                    pid = int(pid_path.read_text().strip())
                    os.kill(pid, 15)  # SIGTERM
                    try:
                        proc_deadline = datetime.now().timestamp() + 5
                        while datetime.now().timestamp() < proc_deadline and _is_running(pid):
                            time.sleep(0.2)
                    except Exception:
                        pass
                except Exception as e:
                    print(f"Failed to stop by pid file: {e}")
                try:
                    pid_path.unlink()
                except Exception:
                    pass
            else:
                # 兜底：尝试通过进程名杀掉 uvicorn main:app
                try:
                    subprocess.call("pkill -f 'uvicorn.*main:app'", shell=True)
                except Exception:
                    pass
            print("stopped")
            sys.exit(0)

    # 默认直接前台运行（开发模式）
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_level="info")