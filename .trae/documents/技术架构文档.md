## 1.Architecture design

```mermaid
graph TD
    A[User Browser] --> B[HTML + Bootstrap + jQuery + fabric.js Frontend]
    B --> C[FastAPI Backend]
    C --> D[File System Storage]
    C --> E[YOLO Training Engine]
    C --> F[WebSocket Service]
    
    subgraph "Frontend Layer"
        B
    end
    
    subgraph "Backend Layer"
        C
        E
        F
    end
    
    subgraph "Data Layer"
        D
    end
```

## 2.Technology Description

- Frontend: HTML5 + CSS3 + <PERSON>trap@5 + jQuery@3 + fabric.js@5
- Backend: FastAPI@0.104 + uvicorn + websockets + ultralytics + asyncio
- Storage: File System (images, labels, models)
- ML Framework: ultralytics (YOLOv8)

## 3.Route definitions

| Route | Purpose |
|-------|----------|
| / | 主界面，四栏式布局的YOLO训练平台 |
| /static/* | 静态资源文件（CSS、JS、图标等） |

## 4.API definitions

### 4.1 Core API

**类别管理相关**
```
POST /api/class/create
```

Request:
| Param Name| Param Type  | isRequired  | Description |
|-----------|-------------|-------------|-------------|
| class_name  | string      | true        | 类别名称 |

Response:
| Param Name| Param Type  | Description |
|-----------|-------------|-------------|
| success    | boolean     | 创建是否成功 |
| message    | string      | 响应消息 |

Example
```json
{
  "class_name": "cat"
}
```

```
GET /api/class/get-all
```

Response:
| Param Name| Param Type  | Description |
|-----------|-------------|-------------|
| classes    | array     | 类别列表 |
| class_name | string    | 类别名称 |
| labeled_count | integer | 有标注样本数 |
| negative_count | integer | 负样本数 |

```
DELETE /api/class/delete
```

Request:
| Param Name| Param Type  | isRequired  | Description |
|-----------|-------------|-------------|-------------|
| class_name  | string      | true        | 要删除的类别名 |

**样本管理相关**
```
POST /api/sample/submit
```

Request:
| Param Name| Param Type  | isRequired  | Description |
|-----------|-------------|-------------|-------------|
| image  | file      | true        | 图片文件 |
| file_name  | string      | false        | 图片文件名 |
| labels  | array      | false        | 标注框数组 |
| target_classes  | array      | true        | 目标类别数组 |
| is_update  | boolean      | false        | 是否为更新操作 |

```
GET /api/sample/get-screenshot
```

Response:
| Param Name| Param Type  | Description |
|-----------|-------------|-------------|
| image_url    | string     | 截图图片URL |
| file_name    | string     | 文件名 |

**模型训练相关**
```
POST /api/train/start
```

Request:
| Param Name| Param Type  | isRequired  | Description |
|-----------|-------------|-------------|-------------|
| model_name  | string      | true        | 模型名称 |
| base_model  | string      | true        | 基底模型文件名 |
| target_classes  | array      | true        | 训练类别数组 |
| train_params  | object      | true        | 训练参数 |

```
WS /ws/train/log
```

WebSocket连接，实时推送训练日志

**模型推理相关**
```
POST /api/infer
```

Request:
| Param Name| Param Type  | isRequired  | Description |
|-----------|-------------|-------------|-------------|
| image  | file      | true        | 图片文件 |
| model_name  | string      | true        | 模型名称 |
| conf_threshold  | float      | false        | 置信度阈值 |

Response:
| Param Name| Param Type  | Description |
|-----------|-------------|-------------|
| results    | array     | 识别结果数组 |
| inference_time    | float     | 推理耗时(秒) |

## 5.Server architecture diagram

```mermaid
graph TD
    A[FastAPI Application] --> B[Router Layer]
    B --> C[Service Layer]
    C --> D[File Manager]
    C --> E[YOLO Engine]
    C --> F[WebSocket Manager]
    D --> G[(File System)]
    E --> H[ultralytics]
    
    subgraph Server
        B
        C
        D
        E
        F
    end
```

## 6.Data model

### 6.1 Data model definition

```mermaid
erDiagram
    CLASS ||--o{ LABEL : has
    CLASS ||--o{ NEGATIVE_SAMPLE : contains
    COMMON_IMAGE ||--o{ LABEL : references
    MODEL ||--o{ CLASS : trained_on
    
    CLASS {
        string name PK
        int labeled_count
        int negative_count
        datetime created_at
    }
    
    LABEL {
        string file_name PK
        string class_name PK
        float x1
        float y1
        float x2
        float y2
        datetime created_at
    }
    
    COMMON_IMAGE {
        string file_name PK
        string original_name
        int width
        int height
        datetime created_at
    }
    
    NEGATIVE_SAMPLE {
        string file_name PK
        string class_name PK
        datetime created_at
    }
    
    MODEL {
        string name PK
        string base_model
        array classes
        string status
        datetime created_at
    }
```

### 6.2 Data Definition Language

**目录结构定义**
```
-- 项目根目录结构
project_root/
├── data/                           -- 核心数据存储区
│   ├── common_images/              -- 公共图片库
│   └── classes/                    -- 类别总目录
│       ├── {class_name}/           -- 具体类别目录
│       │   ├── labels/             -- 标注文件目录
│       │   └── negative/           -- 负样本图片目录
├── models/                         -- 训练完成的模型文件
│   └── {model_name}/               -- 模型目录
│       ├── {model_name}.pt         -- PyTorch模型
│       ├── {model_name}.onnx       -- ONNX模型
│       └── {model_name}.ncnn       -- NCNN模型
├── base_models/                    -- YOLO基底模型
│   ├── yolov8n.pt
│   ├── yolov8s.pt
│   └── yolov8m.pt
├── train_data/                     -- 训练临时目录
├── app_screenshots/                -- app截图文件
├── logs/                           -- 日志文件
└── static/                         -- 前端静态资源
```

**标注文件格式 (YOLO格式)**
```
-- 每行一个标注框，格式：class_id center_x center_y width height
-- 坐标为归一化坐标 (0-1)
0 0.5 0.5 0.3 0.4
1 0.2 0.3 0.1 0.2
```

**模型配置文件格式**
```yaml
-- dataset.yaml
path: /path/to/dataset
train: train
val: val
test: test

names:
  0: class1
  1: class2
  2: class3
```

**训练参数配置**
```python
# 默认训练参数
TRAIN_CONFIG = {
    "epochs": 100,
    "batch": 8,
    "lr0": 0.0005,
    "patience": 20,
    "workers": 12,
    "augment": True,
    "cache": False,
    "device": "auto"
}
```