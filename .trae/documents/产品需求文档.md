## 1. Product Overview

本项目是一个集数据标注与YOLO训练一体的Web应用平台，通过"公共图片库 + 类别独立目录"设计，平衡多类别数据管理灵活性与存储效率。结合fabric.js实现精准标注交互，利用Bootstrap构建响应式布局，依托FastAPI + websockets保障训练、识别流程的实时性与稳定性，最终实现"标注 - 训练 - 推理"一体化的YOLO项目闭环。

该平台主要解决计算机视觉项目中数据标注效率低、模型训练复杂、推理部署困难等问题，为AI开发者和研究人员提供一站式的目标检测解决方案。

## 2. Core Features

### 2.1 Feature Module

我们的YOLO训练平台包含以下主要页面：
1. **主界面**：四栏式响应布局，包含类别管理栏、样本标注区、模型管理与训练栏、识别结果栏

### 2.2 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 主界面 | 类别管理栏 | 创建、编辑、删除类别，显示类别统计信息（有标注数/负样本数），支持拖拽排序 |
| 主界面 | 样本标注区 | 拖拽上传图片，基于fabric.js的标注框创建、编辑、删除，样本流转（获取app截图、验证样本、获取负样本），快捷键支持 |
| 主界面 | 模型管理与训练栏 | 模型选择与删除，训练配置弹窗（epochs、batch、patience等参数），实时训练日志显示，websockets日志传输 |
| 主界面 | 识别结果栏 | 自动/手动识别切换，置信度阈值调整，识别结果展示，识别耗时统计 |

## 3. Core Process

### 数据标注流程
用户首先创建类别，然后通过拖拽上传图片或获取app截图进行标注。选中类别后，在图片上框选区域生成标注框，支持拖拽调整位置和大小。提交时根据标注状态决定存储位置：有标注存储到公共图片库+类别labels目录，无标注存储到类别negative目录。

### 模型训练流程
用户在训练配置弹窗中设置训练参数（轮数、批次、学习率等），选择基底模型和目标类别，启动异步训练任务。训练过程通过websockets实时推送日志，完成后生成pt/onnx/ncnn格式模型文件。

### 识别推理流程
用户选择训练好的模型，上传图片或使用当前标注图片进行识别。可开启自动识别模式，支持置信度阈值调整。识别结果以标注框形式覆盖图片，侧边栏展示类别统计信息。

```mermaid
graph TD
    A[创建类别] --> B[上传/获取图片]
    B --> C[标注图片]
    C --> D[提交样本]
    D --> E[配置训练参数]
    E --> F[启动模型训练]
    F --> G[模型推理识别]
    G --> H[查看识别结果]
    H --> I[调整置信度阈值]
    I --> G
```

## 4. User Interface Design

### 4.1 Design Style

- **主色调**：Bootstrap默认蓝色系（#0d6efd），辅助色为灰色系（#6c757d）
- **按钮样式**：Bootstrap标准按钮，采用圆角设计，支持btn-sm小尺寸适配窄栏
- **字体**：系统默认字体栈，标题使用h5/h6标签，正文14px
- **布局风格**：四栏式网格布局，卡片式组件设计，顶部固定操作区+中间滚动内容区
- **图标风格**：Bootstrap Icons图标库，简洁线性风格

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 主界面 | 类别管理栏 | 固定高度80px顶部操作区，卡片式类别展示，支持1-3列自适应换行，拖拽排序交互 |
| 主界面 | 样本标注区 | 顶部按钮组操作栏，中心标注容器（9:18宽高比），虚线边框拖拽区域，fabric.js标注层 |
| 主界面 | 模型管理与训练栏 | 模型选择下拉框，90%宽度训练按钮，Modal弹窗配置界面，200px高度日志滚动区 |
| 主界面 | 识别结果栏 | 顶部开关和按钮，置信度滑块控件，识别耗时文本，200px高度结果滚动区 |

### 4.3 Responsiveness

本项目采用桌面优先设计，四栏布局按比例分配宽度（40%+30%+20%+10%），高度统一为100vh。各栏内容超出时内部滚动，保持整体布局稳定。支持窗口宽度变化时的自适应调整，类别卡片自动换行，按钮组合理缩放。