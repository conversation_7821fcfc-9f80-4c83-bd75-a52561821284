# 集数据标注与YOLO训练一体的项目需求文档（整合优化版）

## 一、技术栈与核心设计
### （一）技术栈  
前端采用 `html + js + css + jquery + Bootstrap + fabric.js` 构建交互界面；后端基于 `Python FastAPI` 提供服务支持，利用 `ultralytics` 库实现 YOLO 模型训练与推理，结合 `websockets` 实现实时日志传输。  

### （二）核心设计  
通过 **"公共图片库 + 类别独立目录"** 架构，解决多类别样本冗余存储问题：  
- 公共图片库（`common_images` 目录）：统一存储带标注的样本图片，避免多类别重复存储。  
- 类别目录（`classes/[类别名]` 结构）：每个类别独立维护 `labels`（标注文件）和 `negative`（负样本图片）子目录，灵活管理类别专属数据。  

## 二、前端页面布局与功能
页面采用 **四栏式响应布局**，从左到右依次为 **类别管理栏、样本标注区、模型管理与训练栏、识别结果栏**，各栏高度与浏览器窗口高度一致（`100vh`），宽度按比例动态适配，纵向内容超出时内部滚动。  

### （一）类别管理栏（左起第一栏，占比 40% 窗口宽度）  
#### 布局与交互  
- **结构**：顶部固定操作区 + 中间类别卡片滚动区。  
- **核心特性**：  
  - **类别卡片**：固定尺寸，根据容器宽度自动换行（每行 1 - 3 个），支持拖拽排序。卡片显示类别名、"有标注数/负样本数"统计信息，编辑、删除按钮缩小化，节省内部空间。  
  - **拖拽排序**：基于 `Bootstrap` 网格 + 拖拽库实现，拖动卡片可调整类别顺序，释放后触发后端更新类别排序逻辑。  
  - **自适应**：窗口宽度变化时，卡片自动换行，保持布局紧凑；操作按钮（添加/编辑/删除）适配窄栏，采用 `btn-sm` 样式。  

#### 布局结构代码示例
```html
<div class="row h-100">
  <div class="col-3 bg-light border-end">
    <!-- 顶部固定区域 -->
    <div class="d-flex align-items-center justify-content-between p-3" style="height: 80px;">
      <h5 class="mb-0">类别管理</h5>
      <button class="btn btn-primary btn-sm" id="add-class-btn">添加类别</button>
    </div>
    <!-- 中间滚动区域 -->
    <div class="overflow-auto" style="height: calc(100vh - 80px);">
      <!-- 类别卡片列表 -->
      <div class="card m-3" id="class-card-{{class_name}}">
        <div class="card-body">
          <h6 class="card-title">{{class_name}}</h6>
          <p class="card-text">有标注数: {{labeled_count}} / 负样本数: {{negative_count}}</p>
          <div class="d-flex justify-content-end">
            <button class="btn btn-warning btn-sm me-2">修改</button>
            <button class="btn btn-danger btn-sm">删除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

### （二）样本标注区（左起第二栏，占比 30% 窗口宽度）  
#### 布局与交互  
- **结构**：顶部操作栏 + 核心标注容器（含拖拽上传区、图片渲染层、标注框层）。  
- **核心特性**：  
  - **拖拽上传**：上传区在标注容器内绝对居中，支持循环拖拽（可连续上传多张图片）。拖拽时显示文件预览、进度条，非图片文件触发格式校验提示（仅支持 `jpg/png/bmp`）。  
  - **标注功能（fabric.js 实现）**：  
    - 选中类别后，鼠标框选图片区域生成标注框（类别与选中类别一致），框选时显示半透明预览框。  
    - 标注框支持 **拖拽调整位置**、**拖拽四角/边中点调整大小**，选中状态高亮显示调整控制点；右键标注框弹出删除菜单，点击删除当前标注框。  
  - **样本流转**：支持"获取 app 截图""验证样本""获取负样本"功能，切换样本时带淡入淡出过渡（`300ms` 动画），首尾样本切换时按钮置灰并提示。  
  - **快捷键**：绑定 `回车（提交）`、`←/→（切换样本）`、`退格（删除标注框）` 等快捷键，操作时触发按钮高亮反馈，输入框聚焦时快捷键临时失效。  

#### 布局结构代码示例
```html
<div class="col-4 border-end">
  <!-- 顶部控制栏 -->
  <div class="d-flex align-items-center justify-content-between p-3" style="height: 80px;">
    <h5 class="mb-0">样本标注</h5>
    <div class="btn-group">
      <button class="btn btn-success" id="submit-sample-btn">提交样本</button>
      <button class="btn btn-info" id="get-app-screenshot">获取app截图</button>
      <button class="btn btn-primary" id="verify-sample">验证样本</button>
      <button class="btn btn-secondary" id="get-negative-sample">获取负样本</button>
      <button class="btn btn-secondary" id="prev-sample">上一张</button>
      <button class="btn btn-secondary" id="next-sample">下一张</button>
      <button class="btn btn-danger" id="delete-sample">删除</button>
    </div>
  </div>
  <!-- 核心标注区（自适应宽高比 9:18） -->
  <div class="d-flex justify-content-center align-items-center" style="height: calc(100vh - 160px);">
    <div id="annotation-container" class="border dashed border-secondary" style="max-width: 90%; max-height: 45%;">
      <!-- 拖拽上传提示 -->
      <div id="upload-hint" class="d-flex justify-content-center align-items-center h-100">
        <p class="text-muted">拖拽图片到此处或点击上传</p>
      </div>
      <!-- 图片渲染区 -->
      <img id="annotation-image" class="d-none img-fluid" alt="标注图片">
      <!-- 标注框层（绝对定位） -->
      <div id="annotation-boxes" class="position-absolute"></div>
    </div>
  </div>
  <!-- 提交按钮 -->

</div>
```

### （三）模型管理与训练栏（左起第三栏，占比 20% 窗口宽度）  
#### 布局与交互  
- **结构**：模型选择区（`select` 渲染模型列表） + 训练配置弹窗（`modal` 承载完整训练参数） + 训练日志显示区。  
- **核心特性**：  
  - **模型选择**：`form-select` 渲染模型列表，无模型时显示"请先训练模型"默认选项。选中模型后，下方显示模型关联类别列表，支持"复制类别"功能（点击后类别数组自动复制到剪贴板）。  
  - **训练配置弹窗**：点击"开始训练"按钮触发 `modal` 弹窗，承载完整 YOLO 训练参数（轮数、批次、耐心值、工作进程、数据增强等），参数通过 `select`/`input` 提供预设选项（如 `epochs` 可选 `50/100/200/400`），训练按钮占容器宽度 90%。  
  - **日志交互**：训练日志实时显示区，通过 `websockets` 接收后端训练进度（损失值、验证指标等），支持清空日志操作。  

#### 布局结构代码示例
```html
<div class="col-2 border-end">
  <!-- 模型选择 -->
  <div class="p-3">
    <label for="model-select" class="form-label">模型管理</label>
    <select class="form-select" id="model-select">
      <option value="model1">模型1</option>
      <option value="model2">模型2</option>
    </select>
    <div class="d-flex justify-content-between mt-2">
      <button class="btn btn-danger btn-sm" id="delete-model-btn">删除模型</button>
      <button class="btn btn-warning btn-sm" id="copy-classes-btn">复制类别</button>
    </div>
  </div>
  <!-- 训练配置（Modal 弹窗） -->
  <button class="btn btn-warning w-90 mx-auto d-block" data-bs-toggle="modal" data-bs-target="#train-config-modal">开始训练</button>
  <div class="modal fade" id="train-config-modal">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">训练配置</h5>
          <button class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <!-- 训练参数 -->
          <div class="mb-3">
            <label class="form-label">训练轮数 (epochs)</label>
            <select class="form-select" id="train-epochs">
              <option value="50">50</option>
              <option value="100" selected>100</option>
              <option value="200">200</option>
              <option value="400">400</option>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">批次大小 (batch)</label>
            <input type="number" class="form-control" id="train-batch" value="8">
          </div>
          <div class="mb-3">
            <label class="form-label">耐心值 (patience)</label>
            <select class="form-select" id="train-patience">
              <option value="10">10</option>
              <option value="20" selected>20</option>
              <option value="40">40</option>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">工作进程 (workers)</label>
            <select class="form-select" id="train-workers">
              <option value="0">0</option>
              <option value="2">2</option>
              <option value="4">4</option>
              <option value="8">8</option>
              <option value="12" selected>12</option>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">数据增强 (augment)</label>
            <input type="checkbox" class="form-check-input" id="train-augment" checked>
          </div>
          <div class="mb-3">
            <label class="form-label">学习率 (lr0)</label>
            <input type="number" step="0.0001" class="form-control" id="train-lr0" value="0.0005">
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button class="btn btn-primary" id="start-train-btn">确认训练</button>
        </div>
      </div>
    </div>
  </div>
  <!-- 训练日志 -->
  <div class="p-3">
    <h6>训练日志</h6>
    <div class="border p-2" style="height: 200px; overflow-auto;" id="train-log"></div>
    <button class="btn btn-danger btn-sm mt-2" id="clear-train-log">清空</button>
  </div>
</div>
```

### （四）识别结果栏（右起第一栏，占比 10% 窗口宽度）  
#### 布局与交互  
- **结构**：顶部控制区（自动识别开关、手动识别按钮） + 置信度调整条（容器最顶部） + 识别耗时与结果展示区。  
- **核心特性**：  
  - **置信度调整**：滑块置于容器最顶部，拖动实时调整识别置信度阈值，触发重新推理。  
  - **自动/手动识别**：开启"自动识别"后，拖拽上传或切换样本时自动触发推理；手动点击"识别"按钮时，显示"识别中"加载态，避免重复点击。  
  - **结果展示**：识别结果去重展示类别名、平均置信度、出现次数，点击类别项可高亮图片中对应标注框；识别耗时精准统计（仅包含模型推理阶段），排除图片预处理耗时。  

#### 布局结构代码示例
```html
<div class="col-3">
  <div class="p-3">
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="mb-0">识别结果</h5>
      <div class="form-check form-switch">
        <input class="form-check-input" type="checkbox" id="auto-infer-switch">
        <label class="form-check-label" for="auto-infer-switch">自动识别</label>
      </div>
      <button class="btn btn-warning" id="infer-btn">识别</button>
    </div>
    <!-- 置信度滑块 -->
    <div class="my-3">
      <label for="conf-threshold" class="form-label">置信度阈值</label>
      <input type="range" class="form-range" id="conf-threshold" min="0" max="1" step="0.01" value="0.5">
      <p class="text-center" id="conf-value">0.5</p>
    </div>
    <p class="text-muted" id="infer-time">识别耗时：--</p>
    <div class="border p-2" style="height: 200px; overflow-auto;" id="infer-results"></div>
  </div>
</div>
```

## 三、数据标注与流转逻辑  
### （一）标注流程  
1. **上传与暂存**：拖拽/选择图片后，暂存前端内存；提交时根据标注状态决定存储位置（有标注 → 公共图片库 + 类别 `labels` 目录，无标注 → 类别 `negative` 目录）。  
2. **标注交互**：基于 `fabric.js` 实现标注框创建、编辑、删除：  
   - 创建：选中类别后，框选图片区域生成标注框，类别与选中类别绑定。  
   - 编辑：拖拽调整标注框位置/大小，实时同步坐标信息。  
   - 删除：右键标注框弹出删除菜单，点击删除当前标注框，触发后端标注文件更新。  

### （二）样本流转与验证  
- **获取 app 截图**：从后端 `app_screenshots` 目录随机选取图片，渲染至标注区，提交逻辑与拖拽上传一致。  
- **验证样本/负样本**：选择类别后，后端返回关联样本列表（有标注样本 → 公共图片库 + 类别 `labels` 数据，负样本 → 类别 `negative` 图片），前端逐次渲染，支持标注框调整、删除（仅负样本无标注框，仅支持删除操作）。  

### （三）具体功能细节
1. **拖拽上传**：
   - 拖拽区域始终显示提示文字 "拖拽图片到此处或点击上传"
   - 拖拽文件时，区域边框变为蓝色，显示文件预览缩略图
   - 上传过程中显示进度条（位于标注区底部）

2. **标注框操作**：
   - 框选时显示实时预览框（半透明）
   - 选中的标注框显示为红色边框，未选中为蓝色
   - 调整大小时显示辅助线（虚线）

3. **样本切换**：
   - 切换时有淡入淡出过渡效果（300ms）
   - 到达首尾时按钮置灰并提示

4. **键盘快捷键**：
   - 操作时有视觉反馈（如按钮短暂高亮）
   - 焦点在输入框时快捷键暂时失效

## 四、模型管理与训练流程  
### （一）模型管理  
- **模型列表**：通过 `select` 渲染，无模型时显示"请先训练模型"默认选项。支持删除模型（清理 `models` 目录对应文件）、复制模型关联类别（解析模型类别数组，自动复制到剪贴板）。  

### （二）训练配置与执行  
- **完整训练参数**：通过 `modal` 弹窗配置，覆盖 YOLO 训练核心参数（`epochs`/`batch`/`patience`/`workers`/`augment` 等），`cache` 固定为 `False`，避免内存占用。  
- **异步训练**：配置完成后，后端启动异步训练任务，通过 `websockets` 实时推送训练日志（进度、损失值、验证指标）；训练完成后，生成 `pt`/`onnx`/`ncnn` 格式模型文件，自动新增至前端模型列表。  

### （三）训练配置详细参数
训练配置对话框包含以下配置项：
- 基础训练参数：训练轮数（默认100）、学习率（默认0.0005）、批次大小（默认8）
- 数据增强配置：是否开启数据增强（默认开启）
- 模型名输入框：下方展示已有模型列表，点击列表项可将模型名自动填充到输入框
- 类别选择区：展示所有已创建类别，支持多选（勾选后以数组形式传递给后端）
- 基底模型选择：下拉框加载`base_models`目录下所有YOLO模型文件（如yolov8n.pt、yolov8s.pt），为必选配置

## 五、识别推理与结果展示  
### （一）识别流程  
- **自动/手动触发**：开启"自动识别"后，拖拽上传或样本切换时自动推理；点击"识别"按钮手动触发，显示"识别中"加载态。  
- **耗时统计**：仅统计模型推理阶段耗时（排除图片读取、格式转换），精准反馈模型推理效率。  

### （二）结果展示  
- **标注框与类别列表**：识别结果以标注框形式覆盖图片，侧边栏去重展示类别名、平均置信度、出现次数；点击类别项，高亮图片中对应标注框。  
- **置信度调整**：滑块实时调整推理阈值，触发重新推理，动态更新识别结果。  

## 六、后端关键逻辑  
### （一）目录结构
```
项目根目录
├─ data目录（核心数据存储区）
│  ├─ common_images目录（公共图片库：仅存储有标注数据的样本图片，文件名唯一，如img_20240819_1234.jpg）
│  └─ classes目录（类别总目录）
│     ├─ 类别A目录（如"cat"）
│     │  ├─ labels目录（存储该类别的标注文件：文件名与公共图片库图片一致，如img_20240819_1234.txt）
│     │  └─ negative目录（存储该类别的负样本图片：仅无标注的图片文件）
│     └─ 类别B目录（如"dog"）
│        ├─ labels目录（同上，存储该类别的标注文件）
│        └─ negative目录（同上，存储该类别的负样本图片）
├─ models目录（存储训练完成的模型文件：pt/onnx/ncnn格式，以模型名命名文件夹，内部存放对应格式文件）
├─ base_models目录（存储YOLO基底模型文件：如yolov8n.pt、yolov8s.pt、yolov8m.pt，供训练时选择）
├─ train_data目录（训练临时目录：存放训练前准备的临时数据，训练完成后自动清理）
├─ app_screenshots目录（存储原始app截图文件：供"获取app截图"功能调用）
└─ logs目录（存储服务启动日志、孤儿文件修复日志，如orphan_fix_20240819.log）
```

### （二）孤儿文件扫描与修复逻辑
#### 孤儿文件定义
- 类型1：公共图片库孤儿图片——`common_images`目录存在图片文件，但所有类别`labels`目录均无对应文件名的标注文件（即图片无任何类别关联）。
- 类型2：无效标注文件——某类别`labels`目录存在标注文件，但`common_images`目录无对应文件名的图片（即标注无关联图片）。

#### 扫描与修复时机
- 服务启动时（执行`python app.py start`后），在启动UVicorn服务前自动触发扫描，修复完成后再启动API服务。
- 修复过程中生成日志文件，保存到`logs`目录（命名格式：`orphan_fix_YYYYMMDD_HHMMSS.log`），记录扫描结果与修复操作。

### （三）核心API接口
#### 提交样本接口（POST /api/sample/submit）
- 请求参数：
  - image：图片二进制流（拖拽上传/截图获取的图片数据）
  - file_name：图片文件名（若为修改样本则传原文件名，新增则后端生成唯一文件名）
  - labels：标注框数组（格式：[{class_name: "cat", x1: 100, y1: 100, x2: 200, y2: 200}, ...]，无标注则为空数组）
  - target_classes：目标类别数组（如["cat", "dog"]，表示样本归属的类别）
  - is_update：布尔值（true=修改已有样本，false=新增样本）

#### 类别管理接口
1. 创建类别（POST /api/class/create）：
   - 请求参数：class_name（类别名）
   - 处理逻辑：在`classes`目录下创建以类别名为名的文件夹，内部创建`labels`和`negative`子目录。

2. 获取所有类别（GET /api/class/get-all）：
   - 处理逻辑：遍历`classes`目录下所有子目录，统计每个类别的有标注样本数（`labels`目录文件数）和负样本数（`negative`目录图片数）。

3. 删除类别（DELETE /api/class/delete）：
   - 请求参数：class_name（类别名）
   - 处理逻辑：删除`classes`目录下对应类别文件夹（含`labels`目录标注文件、`negative`目录负样本图片）。

#### 训练接口（POST /api/train/start）与WS日志接口（WS /ws/train/log）
- 训练接口请求参数：
  - model_name：训练后模型名
  - base_model：选中的基底模型文件名
  - target_classes：选中的类别数组
  - train_params：训练参数（epochs: 100, lr: 0.0005, batch_size: 8, augment: true等）

#### 识别推理接口（POST /api/infer）
- 请求参数：
  - image：图片二进制流（或file_name：图片文件名，二选一）
  - model_name：选中的模型名
- 处理逻辑：
  1. 加载`models/{model_name}`目录下的模型文件，记录纯识别开始时间，执行推理，计算纯识别耗时。
  2. 将识别结果的归一化坐标转为像素坐标，整理类别名与置信度。

### （四）训练逻辑
1. 异步处理：使用Python多线程或Celery启动训练任务，避免阻塞其他API请求。
2. 进度与指标获取：通过读取训练过程中生成的临时日志文件，或在训练代码中嵌入回调函数，实时采集epoch数、损失值、验证指标，通过WS推送到前端。
3. 模型格式转换：训练完成后，使用ultralytics库导出onnx格式，使用ncnn工具将onnx转为ncnn格式（生成.param和.bin文件），与pt文件一同保存到`models`目录。

### （五）识别逻辑
1. 异步处理：使用Python多线程执行识别任务，避免阻塞其他API请求。
2. 耗时计算：仅统计模型加载完成后，从输入图片到输出识别结果的时间（排除图片读取、格式转换等非识别耗时）。
3. 结果处理：识别结果中的标注框坐标需转为像素坐标（与前端标注框格式一致），包含类别名、置信度，供前端渲染与调整。

## 七、跨端协作与约束  
### （一）数据格式统一  
标注框坐标通过像素值在前端传递，后端转换为 YOLO 归一化坐标存储；返回标注数据时，再转回像素坐标，确保前后端交互格式一致。  

### （二）部署与依赖  
- **环境依赖**：基于 `Ubuntu 22.04` + `Python 3.9.23`，启动脚本自动安装依赖（`fastapi`/`uvicorn`/`ultralytics` 等），使用国内源加速。  
- **服务启停**：`python app.py start` 启动（含孤儿文件修复、依赖安装、服务热重载），`python app.py stop` 终止服务（清理进程）。  

### （三）完整训练配置映射（前端→后端）
前端训练参数通过 API 传递，后端需严格映射到 YOLO 训练函数：
```python
# 前端参数示例
train_params = {
    "epochs": 100,
    "batch": 8,
    "patience": 20,
    "workers": 12,
    "augment": True,
    "lr0": 0.0005,
}

# 后端训练函数调用
results = model.train(
    data=data_yaml,
    epochs=train_params["epochs"],
    batch=train_params["batch"],
    imgsz=640,
    device=device,
    project=output_dir,
    name='train',
    exist_ok=True,
    verbose=True,
    model=model_path,
    plots=False,
    save_period=10,
    patience=train_params["patience"],
    cache=False,  # 固定禁用缓存
    workers=train_params["workers"],
    amp=True,
    cos_lr=True,
    warmup_epochs=3,
    # 极简增强配置
    hsv_h=0.0,
    hsv_s=0.05,
    hsv_v=0.1,
    scale=0.2,
    perspective=0.0,
    flipud=0.0,
    fliplr=0.0,
    mosaic=0.0,
    mixup=0.0,
    copy_paste=0.0,
    erasing=0.0,
    dropout=0.05,
    weight_decay=0.0001,
    lr0=train_params["lr0"]
)
```

## 八、前端交互提示与细节
1. 类别/模型选中状态需高亮（如选中类别加红色边框，选中模型标蓝色背景）。
2. 拖拽非图片文件时，弹出提示"仅支持jpg/png/bmp等图片格式"。
3. 点击"获取app截图"时，若后端`app_screenshots`目录为空，弹出提示"无可用app截图"。
4. 点击"验证样本"或"获取负样本"时，若当前选中类别无对应样本，弹出提示"当前类别无该类型样本"。
5. 服务启动后，前端可接收后端推送的"孤儿文件修复日志"，并在页面顶部短暂弹窗提示。
6. 所有按钮有 hover 效果（颜色加深或轻微放大）。
7. 操作成功/失败有明确提示（顶部短暂弹窗）。
8. 异步操作（如训练、识别）显示加载动画。
9. 模态框（如训练配置）居中显示，背景半透明遮罩。
10. 所有的交互弹窗必须使用modal交互，所有的提示信息必须使用通知提示
## 九、总体宏观逻辑  
通过"公共图片库 + 类别独立目录"设计，平衡多类别数据管理灵活性与存储效率；结合 `fabric.js` 实现精准标注交互，利用 `Bootstrap` 构建响应式布局，依托 `FastAPI` + `websockets` 保障训练、识别流程的实时性与稳定性，最终实现"标注 - 训练 - 推理"一体化的 YOLO 项目闭环。服务启动时自动扫描并修复孤儿文件，避免因异常操作导致的数据不一致，保障后续标注与训练功能稳定。