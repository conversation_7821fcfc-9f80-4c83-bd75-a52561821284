<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLO训练平台</title>
    <link href="static/bootstrap.min.css" rel="stylesheet">
    <link href="static/bootstrap-icons.css" rel="stylesheet">
    <link href="static/style.css" rel="stylesheet">
    <style>
        body { overflow: hidden; }
        .main-container { 
            height: 100vh; 
            padding: 15px 0;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container-fluid main-container">
        <div class="main-row">
            <!-- 类别管理 -->
            <div class="column">
                <div class="column-header">
                    <h5 class="mb-2"><i class="bi bi-tags"></i> 类别管理</h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-sm w-100" onclick="createClass()"><i class="bi bi-plus"></i> 创建类别</button>
                    </div>
                </div>
                <div class="column-content" id="classContainer">
                    <!-- 类别卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 样本标注 -->
            <div class="column">
                <div class="column-header">
                    <h5 class="mb-2"><i class="bi bi-image"></i> 样本标注</h5>
                    <div class="d-flex gap-1 flex-wrap">
                        <button class="btn btn-outline-secondary btn-sm" onclick="getScreenshot()"><i class="bi bi-camera"></i> 截图</button>
                        <button class="btn btn-outline-success btn-sm" onclick="submitSample()"><i class="bi bi-check"></i> 提交</button>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearAnnotations()"><i class="bi bi-trash"></i> 清除</button>
                    </div>
                </div>
                <div class="column-content">
                    <div class="annotation-container" id="annotationContainer">
                        <canvas id="annotationCanvas"></canvas>
                        <div class="text-center text-muted" id="dropHint">
                            <i class="bi bi-cloud-upload" style="font-size: 3rem;"></i>
                            <p>拖拽图片到此处</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型训练 -->
            <div class="column">
                <div class="column-header">
                    <h5 class="mb-2"><i class="bi bi-cpu"></i> 模型训练</h5>
                    <button class="btn btn-success btn-sm w-100" onclick="startTraining()"><i class="bi bi-play"></i> 开始训练</button>
                </div>
                <div class="column-content">
                    <div class="log-area" id="trainingLog">
                        <div class="text-muted">训练日志将在这里显示...</div>
                    </div>
                </div>
            </div>

            <!-- 识别结果 -->
            <div class="column">
                <div class="column-header">
                    <h5 class="mb-2"><i class="bi bi-eye"></i> 识别结果</h5>
                    <div class="mb-2">
                        <select class="form-select form-select-sm" id="modelSelect">
                            <option value="">选择模型</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">置信度阈值</label>
                        <input type="range" class="form-range" id="confThreshold" min="0" max="1" step="0.1" value="0.5">
                        <small class="text-muted">当前: <span id="confValue">0.5</span></small>
                    </div>
                    <button class="btn btn-info btn-sm w-100" onclick="manualInfer()"><i class="bi bi-eye"></i> 手动识别</button>
                </div>
                <div class="column-content">
                    <div class="result-area" id="resultArea">
                        <div class="text-muted">识别结果将在这里显示...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 训练配置模态框 -->
    <div class="modal fade" id="trainConfigModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">训练配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">模型名称</label>
                        <input type="text" class="form-control" id="trainModelName" placeholder="输入模型名称">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">基底模型</label>
                        <select class="form-select" id="baseModelSelect">
                            <option value="yolov8n.pt">YOLOv8n (轻量级)</option>
                            <option value="yolov8s.pt">YOLOv8s (标准)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">训练轮数</label>
                        <input type="number" class="form-control" id="epochs" value="100" min="1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">批次大小</label>
                        <input type="number" class="form-control" id="batchSize" value="8" min="1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">学习率</label>
                        <input type="number" class="form-control" id="learningRate" value="0.0005" step="0.0001">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">早停轮数</label>
                        <input type="number" class="form-control" id="patience" value="20" min="1">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmTraining()">开始训练</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建类别模态框 -->
    <div class="modal fade" id="createClassModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建类别</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">类别名称</label>
                        <input type="text" class="form-control" id="classNameInput" placeholder="输入类别名称">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmCreateClass()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传隐藏input -->
    <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">

    <script src="static/bootstrap.bundle.min.js"></script>
    <script src="static/jquery.min.js"></script>
    <script src="static/fabric.min.js"></script>
    <script src="static/app.js"></script>
</body>
</html>