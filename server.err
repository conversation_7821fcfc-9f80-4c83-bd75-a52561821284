/home/<USER>/.local/lib/python3.9/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/home/<USER>/.local/lib/python3.9/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_path" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [24856]
INFO:     Waiting for application startup.
2025-08-19 18:48:10,198 - main - INFO - YOLO训练平台启动中...
2025-08-19 18:48:10,198 - main - INFO - 开始扫描孤儿文件...
2025-08-19 18:48:10,199 - main - INFO - 更新类别 111 样本计数: 正样本=0, 负样本=0
2025-08-19 18:48:10,199 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-19 18:48:10,200 - main - INFO - 更新类别 444 样本计数: 正样本=0, 负样本=0
2025-08-19 18:48:10,200 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-19 18:48:10,200 - main - INFO - 未发现孤儿文件
2025-08-19 18:48:10,200 - main - INFO - YOLO训练平台启动完成
INFO:     Application startup complete.
ERROR:    [Errno 98] error while attempting to bind on address ('0.0.0.0', 8000): address already in use
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
/home/<USER>/.local/lib/python3.9/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/home/<USER>/.local/lib/python3.9/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_path" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [159497]
INFO:     Waiting for application startup.
2025-08-20 01:48:36,674 - main - INFO - YOLO训练平台启动中...
2025-08-20 01:48:36,675 - main - INFO - 开始扫描孤儿文件...
2025-08-20 01:48:36,676 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 01:48:36,676 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 01:48:36,676 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 01:48:36,677 - main - INFO - 未发现孤儿文件
2025-08-20 01:48:36,677 - main - INFO - YOLO训练平台启动完成
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     ('192.168.10.4', 63175) - "WebSocket /ws/train/log" [accepted]
2025-08-20 01:49:08,506 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 01:55:16,594 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 63573) - "WebSocket /ws/train/log" [accepted]
2025-08-20 01:55:16,702 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 02:04:52,492 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 64113) - "WebSocket /ws/train/log" [accepted]
2025-08-20 02:04:52,558 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 02:05:39,251 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 02:05:39,252 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 02:05:39,253 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 02:05:55,861 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 64180) - "WebSocket /ws/train/log" [accepted]
2025-08-20 02:05:55,912 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 02:12:25,224 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 02:12:25,224 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 02:12:25,225 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
INFO:     Shutting down
2025-08-20 02:21:31,272 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [159497]
/home/<USER>/.local/lib/python3.9/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/home/<USER>/.local/lib/python3.9/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_path" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [170853]
INFO:     Waiting for application startup.
2025-08-20 02:21:37,744 - main - INFO - YOLO训练平台启动中...
2025-08-20 02:21:37,744 - main - INFO - 开始扫描孤儿文件...
2025-08-20 02:21:37,745 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 02:21:37,746 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 02:21:37,746 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 02:21:37,746 - main - INFO - 未发现孤儿文件
2025-08-20 02:21:37,746 - main - INFO - YOLO训练平台启动完成
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     ('192.168.10.4', 65106) - "WebSocket /ws/train/log" [accepted]
2025-08-20 02:21:43,835 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 02:38:22,711 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 50361) - "WebSocket /ws/train/log" [accepted]
2025-08-20 02:38:22,831 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 02:38:57,753 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 02:38:57,754 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 02:38:57,754 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 02:39:40,707 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 02:39:40,708 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 02:39:40,708 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 02:44:27,817 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 50888) - "WebSocket /ws/train/log" [accepted]
2025-08-20 02:44:27,921 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 02:44:53,151 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 02:44:53,151 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 02:44:53,152 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 02:54:15,112 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 51685) - "WebSocket /ws/train/log" [accepted]
2025-08-20 02:56:16,200 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 03:00:52,532 - main - INFO - WebSocket连接断开，当前连接数: 0
INFO:     connection closed
INFO:     ('192.168.10.4', 51965) - "WebSocket /ws/train/log" [accepted]
2025-08-20 03:00:52,628 - main - INFO - WebSocket连接建立，当前连接数: 1
INFO:     connection open
2025-08-20 03:06:51,237 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 03:06:51,238 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 03:06:51,238 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
2025-08-20 03:08:02,725 - main - INFO - 更新类别 333 样本计数: 正样本=0, 负样本=0
2025-08-20 03:08:02,726 - main - INFO - 更新类别 1111 样本计数: 正样本=0, 负样本=0
2025-08-20 03:08:02,726 - main - INFO - 更新类别 222 样本计数: 正样本=0, 负样本=0
