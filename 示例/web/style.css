html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
}
body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}
.topbar {
    height: 60px;
    min-height: 60px;
    background: #fff;
    border-bottom: 1.5px solid #bfc4ca;
    z-index: 1100;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    position: relative;
    width: 100%;
}
.footer-bar {
    height: 60px;
    min-height: 60px;
    background: #23272b;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
    position: relative;
    width: 100%;
}
.main-content {
    flex: 1 1 auto;
    min-height: 0;
    min-width: 0;
    display: flex;
    flex-direction: row;
    background: #f4f6fa;
    overflow: hidden;
}
.sidebar-left, .sidebar-right {
    height: 100%;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    border-top: 1px solid #dee2e6;
}
.sidebar-left {
    border-right: 2px solid #bfc4ca;
    flex: 1 1 0;
    min-width: 0;
    max-width: none;
    display: flex;
    flex-direction: column;
    padding-top: 8px;
    padding-bottom: 8px;
}
.sidebar-left h5 {
    margin-bottom: 8px;
}
#submit-sample {
    margin-bottom: 8px;
}
.sidebar-right {
    border-left: 2px solid #bfc4ca;
    flex: 1 1 0;
    min-width: 0;
    max-width: none;
    border-left: 1px solid #dee2e6;
}
.center-area {
    width: 450px; /* 调整后的宽度 */
    min-width: 300px; /* 调整后的最小宽度 */
    max-width: 600px; /* 调整后的最大宽度 */
    border-top: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 24px 32px 0 32px;
    height: 100%;
    overflow-y: auto;
}
.dropzone {
    border: 2px dashed #6c757d;
    border-radius: 8px;
    text-align: center;
    color: #6c757d;
    cursor: pointer;
    background: #f0f3f7;
    width: 100%;
    flex: 1 1 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
    margin-bottom: 0;
    min-height: 0;
}
.uploaded-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    margin: auto;
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: #f0f3f7;
}
#image-preview {
    width: 100%;
    min-height: 0;
    text-align: center;
    margin-top: 8px;
}
@media (max-width: 1100px) {
    .center-area { width: 300px; min-width: 200px; max-width: 400px; padding: 12px 4px 0 4px; }
}
@media (max-width: 900px) {
    .main-content { flex-direction: column; height: calc(100vh - 120px); }
    .sidebar-left, .sidebar-right, .center-area { min-width: 0; max-width: 100%; width: 100%; height: auto; }
    .center-area { padding: 16px 8px 0 8px; }
}

/* --- Toast Notification Styles --- */
#toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.toast-notification {
    padding: 12px 20px;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.toast-notification.show {
    opacity: 1;
    transform: translateY(0);
}

.toast-notification.success {
    background-color: rgba(40, 167, 69, 0.9);
    border-left: 5px solid #1d923b;
}

.toast-notification.error {
    background-color: #dc3545;
}

/* --- Right-Click Context Menu for Annotations --- */
.context-menu {
    position: fixed;
    display: none;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 5px 0;
}

.context-menu-item {
    padding: 8px 15px;
    cursor: pointer;
    font-size: 14px;
}

.context-menu-item:hover {
    background-color: #007bff;
    color: #fff;
}

#training-log-container {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-radius: 6px;
    padding: 10px;
    overflow: hidden;
    position: relative;
    flex-grow: 1;
    min-height: 150px;
}

#training-log {
    height: 100%;
    overflow-y: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
    white-space: pre-wrap;
    word-break: break-word;
}

/* Recognition Results */
#recognition-results {
  height: 100%;
  overflow-y: auto;
  padding: 10px;
}

#recognition-results .result-item {
  margin-bottom: 15px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

#recognition-results .result-item img {
  max-width: 100%;
  max-height: 200px; /* Limit the height of cropped images */
  height: auto;
  border-radius: 4px;
  margin-bottom: 5px;
}

#recognition-results .result-item p {
  margin: 0;
  font-size: 0.9em;
  font-weight: 500;
}

#recognition-results .result-header {
  margin-bottom: 10px;
  font-size: 0.95em;
  font-weight: bold;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 5px;
} 