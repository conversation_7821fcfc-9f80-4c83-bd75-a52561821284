// --- Global Variables & DOM Elements ---
let canvas = null;
let currentImageFile = null;
let imageObject = null;
const dropzone = document.getElementById('dropzone');
const modelSelect = document.getElementById('model-select');
const trainingOptionsContainer = document.getElementById('training-options-container');
const recognitionResultsContainer = document.getElementById('recognition-results');
let ws = null;
let trainingCompletionResolver = null;
let currentDatasetInfo = { name: null, class_names: [] };
let appScreenshots = []; // 存储App截图列表

// 置信度滑块全局变量
let confidenceSlider = null;
let confidenceValue = null;

// --- Initialization & Setup ---

window.addEventListener('DOMContentLoaded', () => {
    setupStaticEventListeners();
    // 插入置信度滑块，只插入一次
    if (!document.getElementById('confidence-slider')) {
        const confidenceContainer = document.createElement('div');
        confidenceContainer.style.margin = '16px 0';
        confidenceContainer.innerHTML = `
          <label for="confidence-slider">置信度阈值: <span id="confidence-value">0.7</span></label>
          <input type="range" id="confidence-slider" min="0" max="1" step="0.01" value="0.7" style="width:200px; vertical-align:middle;">
        `;
        recognitionResultsContainer.parentNode.insertBefore(confidenceContainer, recognitionResultsContainer);
    }
    confidenceSlider = document.getElementById('confidence-slider');
    confidenceValue = document.getElementById('confidence-value');
    if (confidenceSlider && confidenceValue) {
        confidenceSlider.addEventListener('input', () => {
            confidenceValue.textContent = confidenceSlider.value;
        });
    }
    // 移除原有日志区域，直接插入进度卡片到该位置，无需黑色外层
    const oldLog = document.querySelector('#training-log');
    if (oldLog) {
        const card = ensureTrainingProgressCard();
        oldLog.parentNode.replaceChild(card, oldLog);
        card.style.background = 'transparent';
        card.style.boxShadow = 'none';
        card.style.border = 'none';
        card.style.padding = '0';
        card.style.margin = '0';
    }
});

function preventDefaults(e) {
        e.preventDefault();
    e.stopPropagation();
}

function showToast(message, type = 'success', duration = 3000) {
    const container = document.getElementById('toast-container');
    if (!container) return;
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;
    toast.textContent = message;
    container.appendChild(toast);
    setTimeout(() => toast.classList.add('show'), 100);
    setTimeout(() => {
        toast.classList.remove('show');
        toast.addEventListener('transitionend', () => toast.remove());
    }, duration);
}

function setupStaticEventListeners() {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropzone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    ['dragenter', 'dragover'].forEach(eventName => dropzone.addEventListener(eventName, () => dropzone.style.background = '#e9ecef', false));
    ['dragleave', 'drop'].forEach(eventName => dropzone.addEventListener(eventName, () => dropzone.style.background = '#f0f3f7', false));
    dropzone.addEventListener('drop', handleDrop, false);

    fetchSystemInfo();
    loadModels();
    loadDatasets();
    setupWebSocket();

    document.getElementById('create-dataset-btn').addEventListener('click', () => new bootstrap.Modal(document.getElementById('createDatasetModal')).show());
    document.getElementById('confirmCreateDataset').addEventListener('click', handleCreateDataset);
    document.getElementById('start-training-btn').addEventListener('click', handleStartTraining);
    document.getElementById('submit-sample').addEventListener('click', handleSubmitSample);
    document.getElementById('annotate-app-screenshot').addEventListener('click', handleAnnotateAppScreenshot);
    modelSelect.addEventListener('change', handleModelSelectionChange);
    
    // 初始加载App截图列表
    fetchAppScreenshots();
}

// This function holds ALL listeners that depend on a live canvas instance.
function setupAllCanvasListeners() {
    if (!canvas) return;

    // --- START: Right-Click Context Menu Logic ---
    let contextMenu = document.getElementById('annotation-context-menu');
    if (!contextMenu) {
        contextMenu = document.createElement('div');
        contextMenu.id = 'annotation-context-menu';
        contextMenu.className = 'context-menu';
        contextMenu.innerHTML = `<div class="context-menu-item" id="delete-annotation-btn">删除</div>`;
        document.body.appendChild(contextMenu);
    }
    let targetObjectForMenu = null;

    // Prevent default context menu & handle custom menu
    canvas.wrapperEl.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        // 使用 fabric 的 findTarget 判断鼠标下是否有 rect
        const pointer = canvas.getPointer(e);
        const target = canvas.findTarget(e, true);
        console.log('contextmenu event', target);
        if (target && target.type === 'rect') {
            targetObjectForMenu = target;
            contextMenu.style.display = 'block';
            contextMenu.style.left = `${e.clientX}px`;
            contextMenu.style.top = `${e.clientY}px`;
            console.log('show custom menu');
        } else {
            contextMenu.style.display = 'none';
            console.log('right click, but not on rect');
        }
    });

    // Delete action
    const deleteBtn = contextMenu.querySelector('#delete-annotation-btn');
    deleteBtn.onclick = () => {
        if (targetObjectForMenu) {
            if (targetObjectForMenu.label) canvas.remove(targetObjectForMenu.label);
            canvas.remove(targetObjectForMenu);
            canvas.requestRenderAll();
        }
        contextMenu.style.display = 'none';
    };

    // Hide menu on click outside
    window.addEventListener('click', (e) => {
        if (contextMenu.style.display === 'block' && !contextMenu.contains(e.target)) {
            contextMenu.style.display = 'none';
        }
    });
    // --- END: Right-Click Context Menu Logic ---

    // --- Drawing and Interaction Listeners (with Boundary Constraints) ---
    canvas.on('mouse:down', onMouseDown);
    canvas.on('mouse:move', onMouseMove);
    canvas.on('mouse:up', onMouseUp);
    canvas.on('mouse:dblclick', onDoubleClick);
    
    canvas.on('object:moving', (e) => {
        const obj = e.target;
        obj.setCoords();
        const rect = obj.getBoundingRect();
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        if (rect.left < 0) obj.left = 0;
        if (rect.top < 0) obj.top = 0;
        if (rect.left + rect.width > canvasWidth) obj.left = canvasWidth - rect.width;
        if (rect.top + rect.height > canvasHeight) obj.top = canvasHeight - rect.height;
        updateLabelOnTransform(e);
    });

    canvas.on('object:scaling', (e) => {
        const obj = e.target;
        const rect = obj.getBoundingRect();
        if (rect.left < 0 || rect.top < 0 || rect.left + rect.width > canvas.width || rect.top + rect.height > canvas.height) {
            obj.scaleX = obj.lastScaleX || obj.scaleX;
            obj.scaleY = obj.lastScaleY || obj.scaleY;
            obj.left = obj.lastLeft || obj.left;
            obj.top = obj.lastTop || obj.top;
        } else {
            obj.lastScaleX = obj.scaleX;
            obj.lastScaleY = obj.scaleY;
            obj.lastLeft = obj.left;
            obj.lastTop = obj.top;
        }
        updateLabelOnTransform(e);
    });
}

function createContextMenu() {
    const menu = document.createElement('div');
    menu.id = 'annotation-context-menu';
    menu.className = 'context-menu';
    menu.innerHTML = `<div class="context-menu-item" id="delete-annotation-btn">删除</div>`;
    document.body.appendChild(menu);
    return menu;
}

function appendLog(message) {
    const logContainer = document.getElementById('training-log');
    if (logContainer) {
        logContainer.textContent += message + '\n';
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

// 1. 在训练区域插入进度卡片（只插入一次）
function ensureTrainingProgressCard() {
  let card = document.getElementById('training-progress-card');
  if (!card) {
    card = document.createElement('div');
    card.id = 'training-progress-card';
    card.style.display = 'none';
    card.innerHTML = `
      <div style="border:1px solid #eee;padding:16px;border-radius:8px;background:#fafbfc;max-width:400px;">
        <div style="font-weight:bold;">训练进度</div>
        <div id="progress-bar-container" style="background:#e9ecef;height:16px;border-radius:8px;margin:12px 0;overflow:hidden;">
          <div id="progress-bar" style="height:100%;width:0%;background:#4caf50;"></div>
        </div>
        <div id="progress-percent" style="font-size:14px;">0%</div>
        <div id="progress-key-log" style="margin-top:8px;font-size:13px;color:#555;"></div>
      </div>
    `;
    trainingOptionsContainer.appendChild(card);
  }
  return card;
}

function updateTrainingProgress(percent, keyLog) {
  const card = ensureTrainingProgressCard();
  card.style.display = '';
  const bar = card.querySelector('#progress-bar');
  const percentText = card.querySelector('#progress-percent');
  const keyLogText = card.querySelector('#progress-key-log');
  if (bar) bar.style.width = percent + '%';
  if (percentText) percentText.textContent = percent + '%';
  if (keyLogText) keyLogText.textContent = keyLog || '';
  if (percent >= 100) {
    setTimeout(() => { card.style.display = 'none'; }, 2000);
  }
}

// WebSocket连接与重连逻辑
let wsConnected = false;
let wsReconnectTimer = null;
function setupWebSocket() {
  if (ws) {
    try { ws.close(); } catch {}
    ws = null;
  }
  const wsUrl = `ws://${window.location.host}/ws/training-progress`;
  ws = new WebSocket(wsUrl);
  wsConnected = false;
  ws.onopen = () => { wsConnected = true; };
  ws.onclose = () => {
    wsConnected = false;
    // 自动重连，2秒后重试
    if (!wsReconnectTimer) {
      wsReconnectTimer = setTimeout(() => {
        wsReconnectTimer = null;
        setupWebSocket();
      }, 2000);
    }
  };
  ws.onerror = () => {
    wsConnected = false;
    if (!wsReconnectTimer) {
      wsReconnectTimer = setTimeout(() => {
        wsReconnectTimer = null;
        setupWebSocket();
      }, 2000);
    }
  };
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      if (typeof data.percent === 'number') {
        updateTrainingProgress(Math.round(data.percent), data.key_log || '');
      } else if (data.key_log) {
        updateTrainingProgress(0, data.key_log);
      }
    } catch {
      updateTrainingProgress(0, event.data);
    }
  };
}

// --- API & Data Handling ---

async function fetchSystemInfo() {
    try {
        const response = await fetch('/api/system-info');
        const data = await response.json();
        const infoBar = document.getElementById('system-info');
        if (infoBar) {
            infoBar.textContent = `系统信息：Python ${data.python || '-'} | YOLOv8 ${data.yolov8 || '-'} | CLIP/BLIP ${data.clip_blip || '-'} | scikit-learn ${data.scikit_learn || '-'}`;
        }
    } catch (error) {
        console.error('加载系统信息失败:', error);
    }
}

// 获取App截图列表
async function fetchAppScreenshots() {
    try {
        const response = await fetch('/api/app/screenshots');
        const data = await response.json();
        if (response.ok) {
            appScreenshots = data.screenshots || [];
            console.log('获取App截图列表成功:', appScreenshots);
        } else {
            console.error('获取App截图列表失败:', data.error);
        }
    } catch (error) {
        console.error('获取App截图列表网络错误:', error);
    }
}

// 处理标注App截图按钮点击事件
async function handleAnnotateAppScreenshot() {
    // 重新获取最新的截图列表
    await fetchAppScreenshots();
    
    if (appScreenshots.length === 0) {
        showToast('没有可用的App截图，请先从App上传截图。', 'warning');
        return;
    }
    
    const datasetName = document.getElementById('dataset-select').value;
    if (!datasetName) {
        showToast('请在中间栏选择一个要提交到的数据集。', 'error');
        return;
    }
    
    // 获取第一张截图
    const screenshot = appScreenshots[0];
    
    try {
        // 获取截图文件
        const imageResponse = await fetch(`/api/app/screenshots/${screenshot.filename}`);
        if (!imageResponse.ok) {
            throw new Error(`获取截图失败: ${imageResponse.statusText}`);
        }
        
        const imageBlob = await imageResponse.blob();
        const file = new File([imageBlob], screenshot.filename, { type: imageBlob.type });
        
        // 加载图片到画布
        currentImageFile = file;
        const reader = new FileReader();
        reader.onload = (event) => {
            initializeCanvasWithImage(event.target.result, () => {
                handleAutoPredict();
            });
        };
        reader.readAsDataURL(file);
        
        // 显示提示
        showToast(`已加载App截图: ${screenshot.filename}，请进行标注后提交`, 'info');
        
        // 删除已使用的截图
        const deleteResponse = await fetch(`/api/app/screenshots/${screenshot.filename}`, {
            method: 'DELETE'
        });
        
        if (deleteResponse.ok) {
            // 更新截图列表
            await fetchAppScreenshots();
        } else {
            console.error('删除截图失败:', await deleteResponse.json());
        }
    } catch (error) {
        console.error('处理App截图失败:', error);
        showToast(`处理App截图失败: ${error.message}`, 'error');
    }
}
async function loadModels() {
    try {
        const response = await fetch('/api/models');
        const models = await response.json();
        
        const currentSelection = modelSelect.value;
        modelSelect.innerHTML = ''; // Clear

        const baseOption = document.createElement('option');
        baseOption.value = 'yolov8n.pt';
        baseOption.textContent = 'YOLOv8n (用于创建新模型)';
        modelSelect.appendChild(baseOption);

        if (Array.isArray(models)) {
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                modelSelect.appendChild(option);
            });
        }
        
        if (Array.from(modelSelect.options).some(opt => opt.value === currentSelection)) {
            modelSelect.value = currentSelection;
        } else {
             // If previous selection is gone, default to yolov8n and update UI
             modelSelect.value = 'yolov8n.pt';
        }
        handleModelSelectionChange(); // IMPORTANT: Trigger UI update after loading

    } catch (error) {
        showToast(`加载模型列表失败: ${error.message}`, 'error');
    }
}
async function loadDatasets() {
    try {
        const response = await fetch('/api/datasets');
        const data = await response.json();
        const datasetSelect = document.getElementById('dataset-select');
        if (datasetSelect) {
            datasetSelect.innerHTML = '<option value="">请选择一个数据集</option>';
            if (data.success) {
                data.datasets.forEach(dataset => {
                    const option = document.createElement('option');
                    option.value = dataset.name;
                    option.textContent = dataset.name;
                    datasetSelect.appendChild(option);
                });
            }
        }
    } catch (error) {
        showToast(`加载数据集列表失败: ${error.message}`, 'error');
    }
}
async function handleCreateDataset() {
    const datasetNameInput = document.getElementById('datasetName');
    const datasetName = datasetNameInput.value.trim();

    if (!datasetName) {
        showToast('请输入数据集名称。', 'error');
        datasetNameInput.focus();
        return;
    }
    
    try {
        const response = await fetch('/api/datasets', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            // Only send the name
            body: JSON.stringify({ name: datasetName })
        });
        
        const data = await response.json();
        if (response.ok) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('createDatasetModal')).hide();
            datasetNameInput.value = '';
            loadDatasets(); // Refresh list
        } else {
            showToast(`创建失败: ${data.error}`, 'error');
        }
    } catch (error) {
        showToast(`创建数据集时发生网络错误: ${error.message}`, 'error');
    }
}
async function handleStartTraining() {
    const logContainer = document.getElementById('training-log');
    if (logContainer) logContainer.textContent = '';
    
    const modelSelection = modelSelect.value;
    
    // --- THE CRITICAL FIX ---
    // The previous selector was incorrect for radio buttons.
    // This new selector correctly finds either the CHECKED radio button OR the hidden input field.
    const trainingModeInput = document.querySelector('input[name="trainingMode"]:checked, input[name="trainingMode"][type="hidden"]');
    
    if (!trainingModeInput) {
        showToast('无法确定训练模式。', 'error');
        return;
    }
    const trainingMode = trainingModeInput.value;
    
    let newModelName = null;
    if (trainingMode === 'create_new') {
        const newModelNameInput = document.getElementById('new-model-name');
        newModelName = newModelNameInput ? newModelNameInput.value.trim() : null;
        if (!newModelName) {
            showToast('为新模型命名是必填项。', 'error');
            return;
        }
    }
    
    const datasetName = document.getElementById('dataset-select').value;
    if (!datasetName) {
        showToast('请选择一个用于训练的数据集。', 'error');
        return;
    }

    const epochs = document.querySelector('input[name="epochs"]:checked').value;
    const batchSize = document.querySelector('input[name="batch-size"]:checked').value;

    const formData = new FormData();
    formData.append('model_selection', modelSelection);
    formData.append('training_mode', trainingMode);
    if (newModelName) {
        formData.append('new_model_name', newModelName);
    }
    formData.append('dataset_name', datasetName);
    formData.append('epochs', epochs);
    formData.append('batch_size', batchSize);

    try {
        const response = await fetch('/api/yolo/train', { method: 'POST', body: formData });
        const data = await response.json();
        
        if (response.ok) {
            showToast(data.msg, 'success');
            await trainingCompletionPromise();
            showToast('训练成功完成, 正在刷新模型列表...', 'success');
            
            const finalModelName = trainingMode === 'create_new' ? newModelName : modelSelection;
            await loadModels();
            modelSelect.value = finalModelName;
            handleModelSelectionChange();
        } else {
            showToast(`训练启动失败: ${data.msg || data.error}`, 'error');
            appendLog(`❌ 训练启动失败: ${data.msg || data.error}`);
        }
    } catch (error) {
        console.error('启动训练失败:', error);
        showToast('启动训练时发生网络错误。', 'error');
        appendLog('❌ 启动训练时发生网络错误。');
    }
}
async function handleSubmitSample() {
    if (!currentImageFile || !canvas) {
        showToast('请先上传一张图片并进行标注。', 'error');
        return;
    }
    
    const datasetName = document.getElementById('dataset-select').value;
    if (!datasetName) {
        showToast('请在中间栏选择一个要提交到的数据集。', 'error');
        return;
    }

    const annotations = getAnnotationsAsJson();
    if (annotations.annotations.length === 0) {
        showToast('图片上没有任何标注，无法提交。', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('image', currentImageFile, currentImageFile.name);
    // Send annotations as a JSON string
    formData.append('annotations_json', JSON.stringify(annotations));
    formData.append('dataset_name', datasetName);

    try {
        const response = await fetch('/api/samples/upload', {
            method: 'POST',
            body: formData,
        });
        const data = await response.json();
        if (response.ok) {
            showToast(data.message, 'success');
        } else {
            showToast(`提交失败: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('提交样本失败:', error);
        showToast('提交样本时发生网络错误。', 'error');
    }
}
async function handleAutoPredict() {
    const selectedModel = modelSelect.value;
    const isNewTraining = selectedModel === 'yolov8n.pt';

    if (isNewTraining || !currentImageFile || !imageObject) {
        // Clear previous results if user selects "None"
        if (isNewTraining) {
            recognitionResultsContainer.innerHTML = '<p class="text-muted">选择一个已训练的模型进行识别。</p>';
        }
        return;
    }

    const confidence = parseFloat(confidenceSlider.value);
    const formData = new FormData();
    formData.append('image', currentImageFile);
    formData.append('model_name', selectedModel);
    formData.append('confidence', confidence);

    // 获取复选框状态，决定是否跳过裁剪图片
    const skipCroppedImages = document.getElementById('skip-cropped-images').checked;

    recognitionResultsContainer.innerHTML = '<p class="text-muted">正在识别中...</p>';
    
    try {
        // 根据复选框状态决定是否添加type=app参数
        const url = skipCroppedImages ? '/api/yolo/predict?type=app' : '/api/yolo/predict';
        const response = await fetch(url, { method: 'POST', body: formData });
        const data = await response.json();
        if (!data.success) throw new Error(data.error || '预测响应无效');
        
        // 统一处理API响应结构
        if (data.results && data.results.predictions) {
            // 新的API响应结构
            displayPredictionResults(data);
            drawPredictionBoxes(data.results.predictions);
        } else if (data.predictions) {
            // 兼容旧的API响应结构
            const compatData = {
                results: {
                    predictions: data.predictions,
                    model_name: data.model_name,
                    inference_time_ms: data.inference_time_ms
                }
            };
            displayPredictionResults(compatData);
            drawPredictionBoxes(data.predictions);
        } else {
            throw new Error('无法识别的响应格式');
        }
    } catch (error) {
        showToast(`识别出错: ${error.message}`, 'error');
        recognitionResultsContainer.innerHTML = `<p class="text-danger">识别出错: ${error.message}</p>`;
    }
}

// --- UI Event Handlers ---

function handleModelSelectionChange() {
    const selectedValue = modelSelect.value;
    
    if (selectedValue === 'yolov8n.pt') {
        trainingOptionsContainer.innerHTML = `
            <input type="hidden" name="trainingMode" value="create_new">
            <div class="mb-3">
                <label for="new-model-name" class="form-label">新模型名称</label>
                <input type="text" class="form-control" id="new-model-name" placeholder="为新模型命名，例如: face-detector-v1" required>
                <div class="form-text">使用YOLOv8n创建一个全新的模型。</div>
            </div>
        `;
    } else {
        trainingOptionsContainer.innerHTML = `
            <div class="mb-3">
                <label class="form-label">训练模式</label>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="trainingMode" id="modeIncremental" value="retrain_incremental" checked>
                    <label class="form-check-label" for="modeIncremental">
                        增量训练 (仅使用新数据)
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="trainingMode" id="modeFull" value="retrain_full">
                    <label class="form-check-label" for="modeFull">
                        全量重训 (使用所有数据)
                    </label>
                </div>
            </div>
        `;
    }

            if (selectedValue !== 'yolov8n.pt') {
        handleAutoPredict();
    } else {
        recognitionResultsContainer.innerHTML = '<p class="text-muted">选择一个已训练的模型进行识别。</p>';
        if (canvas) {
            canvas.remove(...canvas.getObjects().filter(obj => obj.type !== 'image'));
            canvas.renderAll();
        }
    }
}

function handleTrainingModeChange() {
    const trainingMode = document.querySelector('input[name="trainingMode"]:checked').value;
    const isNewMode = trainingMode === 'new';
    
    newModelNameWrapper.style.display = isNewMode ? 'block' : 'none';
    newModelNameInput.required = isNewMode;
}

async function handleDatasetSelectionChange() {
    const datasetName = document.getElementById('dataset-select').value;
    if (datasetName) {
        try {
            const response = await fetch(`/api/datasets/${datasetName}`);
            const data = await response.json();
            if (data.success) {
                currentDatasetInfo = data;
                showToast(`已选择数据集: ${data.name}`, 'success', 2000);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            showToast(`加载数据集信息失败: ${error.message}`, 'error');
            currentDatasetInfo = { name: null, class_names: [] };
        }
    } else {
        currentDatasetInfo = { name: null, class_names: [] };
    }
}


// --- Canvas, Annotation, and Prediction Logic ---

function handleDrop(e) {
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
        currentImageFile = file;
            const reader = new FileReader();
        reader.onload = (event) => {
            initializeCanvasWithImage(event.target.result, () => {
                handleAutoPredict();
            });
            };
            reader.readAsDataURL(file);
        }
}

function initializeCanvasWithImage(imageUrl, callback) {
    dropzone.innerHTML = ''; 
    imageObject = null; canvas = null;
    const tempImg = new Image();
    tempImg.src = imageUrl;
    tempImg.onload = () => {
        const scale = Math.min(dropzone.clientWidth / tempImg.width, dropzone.clientHeight / tempImg.height);
        const canvasWidth = tempImg.width * scale;
        const canvasHeight = tempImg.height * scale;
        const canvasEl = document.createElement('canvas');
        canvasEl.width = canvasWidth;
        canvasEl.height = canvasHeight;
        dropzone.appendChild(canvasEl);
        canvas = new fabric.Canvas(canvasEl);
        fabric.Image.fromURL(imageUrl, (img) => {
            img.set({
                scaleX: canvas.width / img.width,
                scaleY: canvas.height / img.height,
                selectable: false, evented: false,
            });
            canvas.add(img);
            canvas.sendToBack(img);
            imageObject = img; 
            setupAllCanvasListeners(); 
            if (callback) callback();
        });
    };
}

function drawPredictionBoxes(predictions) {
    canvas.remove(...canvas.getObjects().filter(obj => obj.type !== 'image'));
    if (!canvas) return;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    predictions.forEach((p) => {
        const normBox = p.box_normalized; // 格式为 [x1, y1, x2, y2]
        if (!normBox || normBox.length !== 4) return;
        
        // 计算坐标和尺寸（从xyxy格式转换为x,y,w,h格式）
        const x = normBox[0] * canvasWidth;
        const y = normBox[1] * canvasHeight;
        const w = (normBox[2] - normBox[0]) * canvasWidth;
        const h = (normBox[3] - normBox[1]) * canvasHeight;
        
        createBoxWithLabel(x, y, w, h, p.class_name);
    });
    canvas.renderAll();
}

function getAnnotationsAsJson() {
    if (!canvas || !imageObject) return { img_height: 0, img_width: 0, annotations: [] };
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const annotationData = {
        img_height: imageObject.height,
        img_width: imageObject.width,
        annotations: []
    };
    const objects = canvas.getObjects('rect');
    for (const obj of objects) {
        if (!obj.label) continue;
        const box = obj.getBoundingRect();
        const centerX = (box.left + box.width / 2) / canvasWidth;
        const centerY = (box.top + box.height / 2) / canvasHeight;
        const width = box.width / canvasWidth;
        const height = box.height / canvasHeight;
        const clamp = (num) => Math.max(0, Math.min(1, num));
        annotationData.annotations.push({
            class_name: obj.label.text,
            box_normalized: [clamp(centerX), clamp(centerY), clamp(width), clamp(height)]
        });
    }
    return annotationData;
}

function displayPredictionResults(data) {
    recognitionResultsContainer.innerHTML = '';
    if (!data || !data.results || !data.results.predictions || data.results.predictions.length === 0) {
        recognitionResultsContainer.innerHTML = '<div class="text-muted">未检测到目标或结果格式异常</div>';
        return;
    }
    const scrollContainer = document.createElement('div');
    scrollContainer.style.display = 'flex';
    scrollContainer.style.flexWrap = 'wrap';
    scrollContainer.style.gap = '16px';
    scrollContainer.style.padding = '8px 0';
    data.results.predictions.forEach(item => {
        const card = document.createElement('div');
        card.style.background = '#fff';
        card.style.border = '1px solid #eee';
        card.style.borderRadius = '10px';
        card.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
        card.style.padding = '12px';
        card.style.display = 'flex';
        card.style.flexDirection = 'column';
        card.style.alignItems = 'center';
        card.style.minWidth = '160px';
        card.style.maxWidth = '200px';
        card.style.flex = '0 0 auto';
        card.style.justifyContent = 'space-between';
        // 图片 - 只有当cropped_image_base64存在时才显示图片
        if (item.cropped_image_base64) {
            const img = document.createElement('img');
            img.src = item.cropped_image_base64;
            img.style.maxHeight = '120px';
            img.style.maxWidth = '160px';
            img.style.objectFit = 'contain';
            img.style.borderRadius = '6px';
            img.style.marginBottom = '8px';
            card.appendChild(img);
            
            // 预加载图片以获取原始宽高比并调整大小
            const tempImg = new Image();
            tempImg.onload = function() {
                const ratio = tempImg.width / tempImg.height;
                if (ratio > 1) { // 宽大于高
                    img.style.width = '160px';
                    img.style.height = 'auto';
                } else { // 高大于宽
                    img.style.height = '120px';
                    img.style.width = 'auto';
                }
            };
            tempImg.src = item.cropped_image_base64;
        } else {
            // 如果没有裁剪图像，显示一个占位符或类标签
            const placeholderDiv = document.createElement('div');
            placeholderDiv.style.height = '40px';
            placeholderDiv.style.display = 'flex';
            placeholderDiv.style.alignItems = 'center';
            placeholderDiv.style.justifyContent = 'center';
            placeholderDiv.style.marginBottom = '8px';
            placeholderDiv.style.color = '#666';
            placeholderDiv.textContent = item.class_name || '未知类别';
            card.appendChild(placeholderDiv);
        }
        // 类别名称
        const className = document.createElement('div');
        className.style.fontSize = '15px';
        className.style.fontWeight = 'bold';
        className.textContent = item.class_name || '未知';
        card.appendChild(className);
        // 置信度
        const conf = document.createElement('div');
        conf.style.fontSize = '14px';
        conf.style.color = '#444';
        conf.textContent = `置信度: ${item.confidence !== undefined ? (item.confidence * 100).toFixed(2) + '%' : '未知'}`;
        card.appendChild(conf);
        scrollContainer.appendChild(card);
    });
    recognitionResultsContainer.appendChild(scrollContainer);
}


// --- Drawing Logic ---

let isDrawing = false;
let startX, startY;
let currentRect = null;

function onMouseDown(o) {
    if (o.target || o.e.button !== 0) return;
    isDrawing = true;
    const pointer = canvas.getPointer(o.e);
    startX = Math.max(0, Math.min(pointer.x, canvas.width));
    startY = Math.max(0, Math.min(pointer.y, canvas.height));

    currentRect = new fabric.Rect({
        left: startX, top: startY, width: 0, height: 0,
        fill: 'rgba(0, 123, 255, 0.2)', stroke: '#007bff', strokeWidth: 2,
        hasRotatingPoint: false, lockRotation: true,
    });
    canvas.add(currentRect);
}

function onMouseMove(o) {
    if (!isDrawing) return;
    const pointer = canvas.getPointer(o.e);
    const clampedX = Math.max(0, Math.min(pointer.x, canvas.width));
    const clampedY = Math.max(0, Math.min(pointer.y, canvas.height));

    let width = clampedX - startX;
    let height = clampedY - startY;

    currentRect.set({
        left: width > 0 ? startX : clampedX,
        top: height > 0 ? startY : clampedY,
        width: Math.abs(width),
        height: Math.abs(height)
    });
    canvas.renderAll();
}

function onMouseUp() {
    if (!isDrawing) return;
    isDrawing = false;
    if (currentRect && (currentRect.width < 10 || currentRect.height < 10)) {
        canvas.remove(currentRect);
    } else if (currentRect) {
        createBoxWithLabel(currentRect.left, currentRect.top, currentRect.width, currentRect.height, null);
        canvas.remove(currentRect);
    }
    currentRect = null;
}

function onDoubleClick(options) {
    if (options.target && options.target.type === 'i-text') {
        options.target.enterEditing();
    }
}

function createBoxWithLabel(left, top, width, height, labelText) {
    const defaultLabel = (currentDatasetInfo.class_names.length > 0) ? currentDatasetInfo.class_names[0] : 'face';
    const text = labelText || defaultLabel;

    const rect = new fabric.Rect({
        left, top, width, height,
        fill: 'rgba(0, 123, 255, 0.2)', stroke: '#007bff', strokeWidth: 2,
        hasRotatingPoint: false, lockRotation: true,
        cornerColor: '#007bff', transparentCorners: false, cornerSize: 12,
    });
    addAnnotationLabel(rect, text);
    canvas.add(rect);
    canvas.add(rect.label);
    return rect;
}

function addAnnotationLabel(rect, text) {
    const label = new fabric.IText(text, {
        fontSize: 20, fill: '#ffffff',
        backgroundColor: 'rgba(0, 123, 255, 0.8)', padding: 5,
        textAlign: 'center', originX: 'center', originY: 'top',
        selectable: false, evented: true,
    });
    rect.label = label;
    label.rect = rect;
    updateLabelPosition(rect);
}

function updateLabelPosition(rect) {
    if (!rect || !rect.label) return;
    const label = rect.label;
    const bottomCenter = rect.getPointByOrigin('center', 'bottom');
    label.set({
        left: bottomCenter.x,
        top: bottomCenter.y + 2,
        angle: rect.angle || 0
    }).setCoords();
}

function updateLabelOnTransform({ target }) {
    if (target && target.type === 'rect' && target.label) {
        updateLabelPosition(target);
    }
}

function trainingCompletionPromise() {
    return new Promise(resolve => {
        trainingCompletionResolver = resolve;
    });
}
