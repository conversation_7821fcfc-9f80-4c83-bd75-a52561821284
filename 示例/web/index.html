<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能识别系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/web/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
</head>
<body>
    <!-- Toast Notification Container -->
    <div id="toast-container"></div>
    
    <!-- 顶部栏 -->
    <div class="topbar">
        <div class="container-fluid d-flex align-items-center justify-content-start">
            <button class="btn btn-outline-primary me-2" id="btn-yolo">YOLO训练</button>
            <button class="btn btn-outline-secondary" id="btn-clipmlp">CLIP+MLP训练</button>
        </div>
    </div>
    <div class="main-content">
            <!-- 左侧栏：图片上传与标注 -->
        <div class="sidebar-left p-3">
                <h5>图片上传与标注</h5>
            <div class="d-flex mb-2">
                <button class="btn btn-primary flex-grow-1 me-2" id="submit-sample">提交样本</button>
                <button class="btn btn-success flex-grow-1" id="annotate-app-screenshot">标注App截图</button>
            </div>
            <div class="dropzone flex-grow-1 d-flex align-items-center justify-content-center" id="dropzone">
                    拖拽图片到此处上传
                </div>
            <!-- Removed image-preview and annotation-tools divs -->
            </div>
            <!-- 中间栏：模型与数据集管理 -->
        <div class="center-area" id="center-area">
                <h5>模型与数据集管理</h5>
            
            <!-- Unified Model Selection -->
            <div class="mb-3">
              <label for="model-select" class="form-label">选择模型</label>
              <select class="form-select" id="model-select">
                <!-- Loaded dynamically -->
              </select>
            </div>

            <!-- Training Options Container (Dynamic) -->
            <div id="training-options-container">
              <!-- This will be populated by JavaScript based on model selection -->
            </div>
            
            <hr class="my-3">

            <!-- Dataset Management -->
                <div class="mb-3">
              <label for="dataset-select" class="form-label">数据集</label>
              <div class="input-group">
                    <select class="form-select" id="dataset-select" style="flex: 1;">
                        <option value="">选择一个数据集进行训练</option>
                    </select>
                    <button class="btn btn-outline-success" id="create-dataset-btn" title="创建新数据集">+</button>
                </div>
            </div>
            
            <!-- 训练配置区 -->
                <div class="mb-3">
                <label class="form-label">训练轮数</label>
                <div class="d-flex flex-wrap">
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="epochs" id="epochs-50" value="50">
                        <label class="form-check-label" for="epochs-50">50 (快速测试)</label>
                    </div>
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="epochs" id="epochs-100" value="100" checked>
                        <label class="form-check-label" for="epochs-100">100 (推荐)</label>
                    </div>
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="epochs" id="epochs-200" value="200">
                        <label class="form-check-label" for="epochs-200">200 (高精度)</label>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">批次大小</label>
                <div class="d-flex flex-wrap">
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="batch-size" id="batch-size-4" value="4">
                        <label class="form-check-label" for="batch-size-4">4 (低显存 &lt;2GB)</label>
                    </div>
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="batch-size" id="batch-size-8" value="8">
                        <label class="form-check-label" for="batch-size-8">8 (推荐 &lt;4GB 显存)</label>
                    </div>
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="batch-size" id="batch-size-16" value="16" checked>
                        <label class="form-check-label" for="batch-size-16">16 (推荐 8GB 显存)</label>
                    </div>
                    <div class="form-check me-3 mb-2">
                        <input class="form-check-input" type="radio" name="batch-size" id="batch-size-32" value="32">
                        <label class="form-check-label" for="batch-size-32">32 (推荐 &gt;12GB 显存)</label>
                    </div>
                </div>
            </div>

            <!-- 开始训练按钮 -->
            <div class="mt-4">
                <button class="btn btn-warning w-100 py-3 fs-5 fw-bold" id="start-training-btn">
                    🚀 开始训练
                </button>
            </div>
            
            <!-- 训练日志显示区 -->
            <div id="training-log-container" class="mt-3 flex-grow-1">
                <pre id="training-log" class="h-100">训练日志将显示在这里...</pre>
            </div>
        </div>
        
            <!-- 右侧栏：识别结果 -->
        <div class="sidebar-right p-3 d-flex flex-column">
                <h5>识别结果</h5>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="skip-cropped-images">
                <label class="form-check-label" for="skip-cropped-images">跳过裁剪图片（适用于移动端）</label>
            </div>
            <div id="recognition-results" class="flex-grow-1 mt-2">
                <p class="text-muted">上传图片并选择推理模型后，将在此处显示结果。</p>
            </div>
        </div>
    </div>
    
    <!-- 创建数据集模态框 -->
    <div class="modal fade" id="createDatasetModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新数据集</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="create-dataset-form" novalidate>
                        <div class="mb-3">
                            <label for="datasetName" class="form-label">数据集名称</label>
                            <input type="text" class="form-control" id="datasetName" placeholder="输入数据集名称，如 my-faces-v1" required>
                             <div class="form-text">
                              后续训练时，系统将自动从您的标注中收集所有类别。
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmCreateDataset">创建</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部栏 -->
    <div class="footer-bar">
        <span id="system-info">系统信息：Python 3.x | YOLOv8 | CLIP/BLIP | scikit-learn</span>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/web/main.js"></script>
</body>
</html>
